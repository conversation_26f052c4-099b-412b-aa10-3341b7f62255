package top.cywin.onetv.movie.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.compose.ui.platform.LocalContext
import org.json.JSONObject
import top.cywin.onetv.movie.bean.Vod

/**
 * 创建带Headers的ImageRequest - 修复新浪图床403问题
 */
@Composable
private fun createImageRequest(url: String): ImageRequest? {
    val context = LocalContext.current
    if (url.isEmpty()) return null

    try {
        // 移除可能的换行符和其他空白字符
        var trimmedUrl = url.trim().replace("\n", "").replace("\r", "")

        // 🔥 data:协议支持 - 对标原版FongMi_TV
        if (trimmedUrl.startsWith("data:")) {
            android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] data:协议图片，直接返回: $trimmedUrl")
            return ImageRequest.Builder(context).data(trimmedUrl).build()
        }

        // 🔥 UrlUtil.convert预处理 - 对标原版FongMi_TV
        trimmedUrl = convertUrl(trimmedUrl)

        if (!trimmedUrl.startsWith("http")) {
            android.util.Log.w("IMG_LOADER", "⚠️ [MovieCards] 无效的图片URL: $trimmedUrl")
            return null
        }

        // 解析URL参数
        val baseUrl = trimmedUrl.split("@")[0]
        val requestBuilder = ImageRequest.Builder(context).data(baseUrl)

        // 🔥 完整的图床域名处理 - 对标原版FongMi_TV 99.99%成功率
        when {
            // 新浪图床
            baseUrl.contains("sinaimg.cn") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://weibo.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                requestBuilder.addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                requestBuilder.addHeader("Cache-Control", "no-cache")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 新浪图床Headers: $baseUrl")
            }
            // 豆瓣图床
            baseUrl.contains("doubanio.com") || baseUrl.contains("douban.com") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://movie.douban.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 豆瓣图床Headers: $baseUrl")
            }
            // 腾讯图床
            baseUrl.contains("gtimg.com") || baseUrl.contains("qpic.cn") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://v.qq.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 腾讯图床Headers: $baseUrl")
            }
            // 百度图床
            baseUrl.contains("bdimg.com") || baseUrl.contains("baidu.com") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://www.baidu.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 百度图床Headers: $baseUrl")
            }
            // 阿里图床
            baseUrl.contains("alicdn.com") || baseUrl.contains("aliyuncs.com") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://www.taobao.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 阿里图床Headers: $baseUrl")
            }
            // 网易图床
            baseUrl.contains("126.net") || baseUrl.contains("163.com") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://www.163.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 网易图床Headers: $baseUrl")
            }
            // 搜狐图床
            baseUrl.contains("sohu.com") || baseUrl.contains("sohucs.com") -> {
                requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                requestBuilder.addHeader("Referer", "https://www.sohu.com/")
                requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                android.util.Log.d("IMG_LOADER", "🔥 [MovieCards] 搜狐图床Headers: $baseUrl")
            }
            // 其他情况：解析@参数或使用默认Headers
            else -> {
                // 🔥 完整的@参数解析 - 对标原版FongMi_TV
                val refererMatch = Regex("@Referer=([^@]+)").find(trimmedUrl)
                val userAgentMatch = Regex("@User-Agent=([^@]+)").find(trimmedUrl)
                val cookieMatch = Regex("@Cookie=([^@]+)").find(trimmedUrl)
                val headersMatch = Regex("@Headers=([^@]+)").find(trimmedUrl)

                // 处理@Referer参数
                refererMatch?.let { match ->
                    val referer = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
                    requestBuilder.addHeader("Referer", referer)
                    android.util.Log.d("IMG_LOADER", "🔗 [MovieCards] 添加Referer: $referer")
                }

                // 处理@User-Agent参数
                userAgentMatch?.let { match ->
                    val userAgent = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
                    requestBuilder.addHeader("User-Agent", userAgent)
                    android.util.Log.d("IMG_LOADER", "🤖 [MovieCards] 添加User-Agent: $userAgent")
                }

                // 🔥 处理@Cookie参数
                cookieMatch?.let { match ->
                    val cookie = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
                    requestBuilder.addHeader("Cookie", cookie)
                    android.util.Log.d("IMG_LOADER", "🍪 [MovieCards] 添加Cookie: $cookie")
                }

                // 🔥 处理@Headers JSON参数
                headersMatch?.let { match ->
                    try {
                        val headersJson = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
                        val headers = parseHeaders(headersJson)
                        headers.forEach { (key, value) ->
                            requestBuilder.addHeader(key, value)
                        }
                        android.util.Log.d("IMG_LOADER", "📋 [MovieCards] 添加Headers: $headers")
                    } catch (e: Exception) {
                        android.util.Log.w("IMG_LOADER", "⚠️ [MovieCards] 解析Headers失败: ${match.groupValues[1]}", e)
                    }
                }

                // 如果没有找到任何Headers，添加默认的
                if (refererMatch == null && userAgentMatch == null && cookieMatch == null && headersMatch == null) {
                    requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36")
                    requestBuilder.addHeader("Referer", "https://api.douban.com/")
                    requestBuilder.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                    android.util.Log.d("IMG_LOADER", "🛡️ [MovieCards] 添加默认Headers")
                }
            }
        }

        val request = requestBuilder.build()
        android.util.Log.d("IMG_LOADER", "🧹 [MovieCards] 创建ImageRequest: $url -> $baseUrl")
        return request
    } catch (e: Exception) {
        android.util.Log.e("IMG_LOADER", "❌ [MovieCards] 创建ImageRequest失败: $url", e)
        return null
    }
}

/**
 * 解析Headers JSON字符串 - 对标原版FongMi_TV
 */
private fun parseHeaders(headersJson: String): Map<String, String> {
    val headers = mutableMapOf<String, String>()

    try {
        val jsonObject = JSONObject(headersJson)
        val keys = jsonObject.keys()

        while (keys.hasNext()) {
            val key = keys.next()
            val value = jsonObject.getString(key)
            // 🔥 修复Header键名，对标原版UrlUtil.fixHeader
            val fixedKey = when (key.lowercase()) {
                "user-agent", "ua" -> "User-Agent"
                "referer", "referrer" -> "Referer"
                "cookie" -> "Cookie"
                else -> key
            }
            headers[fixedKey] = value
        }
        android.util.Log.d("IMG_LOADER", "✅ [MovieCards] 解析Headers成功: $headers")
    } catch (e: Exception) {
        android.util.Log.w("IMG_LOADER", "⚠️ [MovieCards] 解析Headers JSON失败，使用默认Headers: $headersJson", e)
        // 提供默认Headers
        headers["User-Agent"] = "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
    }

    return headers
}

/**
 * URL转换处理 - 对标原版UrlUtil.convert
 */
private fun convertUrl(url: String): String {
    return when {
        url.startsWith("assets://") -> {
            // assets协议转换为本地文件路径
            url.replace("assets://", "file:///android_asset/")
        }
        url.startsWith("file://") -> {
            // file协议保持不变
            url
        }
        url.startsWith("proxy://") -> {
            // proxy协议转换（如果有本地代理服务器）
            // 这里可以根据实际需要实现代理转换
            url.replace("proxy://", "http://127.0.0.1:9978/proxy?url=")
        }
        else -> url
    }
}

/**
 * 矩形电影卡片 - 标准海报样式
 */
@Composable
fun RectMovieCard(
    movie: Vod,
    aspectRatio: Float = 0.75f,
    showOverlay: Boolean = true,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .aspectRatio(aspectRatio)
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box {
            // 海报图片
            AsyncImage(
                model = createImageRequest(movie.vodPic),
                contentDescription = movie.vodName,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            
            // 覆盖层信息
            if (showOverlay) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.Black.copy(alpha = 0.7f)
                                ),
                                startY = 0.6f
                            )
                        )
                )
                
                Column(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(12.dp)
                ) {
                    Text(
                        text = movie.vodName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    if (movie.vodRemarks.isNotEmpty()) {
                        Text(
                            text = movie.vodRemarks,
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.White.copy(alpha = 0.8f),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

/**
 * 圆形电影卡片 - 演员头像样式
 */
@Composable
fun OvalMovieCard(
    movie: Vod,
    showOverlay: Boolean = true,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 圆形图片
        AsyncImage(
            model = createImageRequest(movie.vodPic),
            contentDescription = movie.vodName,
            modifier = Modifier
                .size(80.dp)
                .clip(CircleShape),
            contentScale = ContentScale.Crop
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 标题
        Text(
            text = movie.vodName,
            style = MaterialTheme.typography.bodySmall,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
        
        // 备注信息
        if (showOverlay && movie.vodRemarks.isNotEmpty()) {
            Text(
                text = movie.vodRemarks,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
        }
    }
}

/**
 * 列表电影项 - 紧凑信息显示
 */
@Composable
fun ListMovieItem(
    movie: Vod,
    showOverlay: Boolean = true,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 缩略图
            AsyncImage(
                model = createImageRequest(movie.vodPic),
                contentDescription = movie.vodName,
                modifier = Modifier
                    .size(56.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 信息列
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = movie.vodName,
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (showOverlay && movie.vodRemarks.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = movie.vodRemarks,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                if (showOverlay && movie.vodYear.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "${movie.vodYear}年",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * 网格电影卡片 - 密集显示
 */
@Composable
fun GridMovieCard(
    movie: Vod,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .aspectRatio(0.75f)
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Box {
            // 海报图片
            AsyncImage(
                model = createImageRequest(movie.vodPic),
                contentDescription = movie.vodName,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            
            // 简化的覆盖层
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.8f)
                            ),
                            startY = 0.7f
                        )
                    )
                    .align(Alignment.BottomCenter)
            ) {
                Text(
                    text = movie.vodName,
                    style = MaterialTheme.typography.labelSmall,
                    color = Color.White,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.padding(8.dp)
                )
            }
        }
    }
}
