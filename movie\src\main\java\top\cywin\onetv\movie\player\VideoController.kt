package top.cywin.onetv.movie.player

import android.app.Activity
import android.util.Log
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.compose.runtime.*
import top.cywin.onetv.movie.Setting
import top.cywin.onetv.movie.bean.History
import top.cywin.onetv.movie.bean.Track
import top.cywin.onetv.movie.bean.Result
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * 播放器控制器 - 直接移植原版控制逻辑
 * 基于：FongMi_TV/src/leanback/java/top/cywin/onetv/vod/ui/activity/VideoActivity.java
 */
class VideoController(private val activity: Activity) {
    private var _mPlayers: Players? = null
    private var mHistory: History? = null

    // 播放器状态
    var isInitialized by mutableStateOf(false)
        private set

    // 🔥 添加ViewModel状态回调接口
    private var onPlayerStateChanged: ((String, <PERSON>, Long) -> Unit)? = null

    // 公共访问播放器实例
    val mPlayers: Players? get() = _mPlayers

    // 🔥 新增：获取播放器实例的方法，用于全屏模式复用
    fun getPlayers(): Players? = _mPlayers

    // 🔥 新增：获取历史记录的方法，用于片头片尾跳过
    fun getHistory(): History? = mHistory

    // 🔥 新增：回调接口 - 用于片头片尾跳过功能
    var onNextEpisodeCallback: (() -> Unit)? = null
    private var _onResetCallback: ((Boolean) -> Unit)? = null
    private var _onChangeSourceCallback: (() -> Unit)? = null
    
    fun initialize(): Players? {
        // 🔥 添加VOD_FLOW日志跟踪播放器初始化
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

        if (_mPlayers == null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT] 播放器初始化开始")
            _mPlayers = Players.create(activity)

            // 🔥 关键修复：设置VideoController引用，支持播放中断处理
            _mPlayers?.setVideoController(this)

            // 🔥 修复：立即启用时间变化监听器，确保观看历史能正确保存
            enableOpeningEndingSkip()

            isInitialized = true
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_SUCCESS] 播放器初始化完成，时间监听器已启用")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_INIT", "播放器初始化完成")
        } else {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_REUSE] 复用现有播放器实例")
        }
        return _mPlayers
    }
    
    fun setHistory(history: History) {
        mHistory = history
    }

    // 🔥 设置ViewModel状态回调
    fun setOnPlayerStateChangedCallback(callback: (String, Long, Long) -> Unit) {
        onPlayerStateChanged = callback
        Log.d("VOD_FLOW", "[PLAYER_CALLBACK] ViewModel状态回调已设置")
    }

    // 🔥 通知ViewModel播放器状态变化
    fun notifyPlayerStateChanged(state: String, position: Long = 0, duration: Long = 0) {
        onPlayerStateChanged?.invoke(state, position, duration)
        Log.v("VOD_FLOW", "[PLAYER_CALLBACK] 通知ViewModel状态变化: $state, position=$position, duration=$duration")
    }
    
    // 直接移植原版方法：onScale()
    fun onScale(): String {
        val index = getScale()
        val scaleArray = arrayOf("默认", "16:9", "4:3", "填充", "原始", "裁剪")
        val newIndex = if (index == scaleArray.size - 1) 0 else index + 1
        mHistory?.setScale(newIndex)
        Setting.putScale(newIndex)
        setScale(newIndex)
        return scaleArray[newIndex]
    }
    
    private fun getScale(): Int {
        return Setting.getScale()
    }
    
    private fun setScale(index: Int) {
        // 直接移植原版setScale逻辑
        _mPlayers?.setVideoScale(index)
    }

    // 直接移植原版方法：onSpeed() - 优化版本
    fun onSpeed(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_SPEED] 播放速度调整")

        return safePlayerOperation("1.00") { players ->
            val result = players.addSpeed()
            mHistory?.setSpeed(players.speed)
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_SPEED_SUCCESS] 播放速度调整为: $result")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_SPEED", "播放速度调整为: $result")
            result
        }
    }

    // 直接移植原版方法：onSpeedAdd()
    fun onSpeedAdd(): String {
        val result = _mPlayers?.addSpeed(0.25f) ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onSpeedSub()
    fun onSpeedSub(): String {
        val result = _mPlayers?.subSpeed(0.25f) ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onSpeedLong()
    fun onSpeedLong(): String {
        val result = _mPlayers?.toggleSpeed() ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onDecode()
    fun onDecode(): String {
        _mPlayers?.toggleDecode()
        setDecode()
        return _mPlayers?.decodeText ?: "软解"
    }
    
    private fun setDecode() {
        // 直接移植原版setDecode逻辑
        // 原版会更新UI显示当前解码模式
    }
    
    // 🔥 完整移植原版方法：onLoop() - 添加VOD_FLOW日志
    fun onLoop(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LOOP_TOGGLE] 循环播放切换")

        val isActivated = !(mHistory?.isLoop() ?: false)
        mHistory?.setLoop(isActivated)

        val result = if (isActivated) "循环开" else "循环关"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LOOP_TOGGLE_SUCCESS] 循环状态: $result")
        VodFlowTracker.logFlowStep(currentFlowId, "LOOP", "循环状态: $result")

        return result
    }

    // 🔥 新增：启用片头片尾自动跳过监听 - 对标原版FongMi_TV
    private fun enableOpeningEndingSkip() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SKIP_ENABLE] 启用片头片尾自动跳过监听")

        mPlayers?.enableOpeningEndingSkip(
            object : Players.OnTimeChangedListener {
                override fun onTimeChanged(position: Long, duration: Long) {
                    <EMAIL>(position, duration)
                }
            },
            object : Players.OnNextEpisodeListener {
                override fun onNextEpisode() {
                    onNextEpisodeCallback?.invoke()
                }
            }
        )
    }

    // 🔥 新增：外部时间变化监听器
    private var externalTimeChangedListener: ((Long, Long) -> Unit)? = null

    /**
     * 设置外部时间变化监听器
     * 🔥 修复：供MoviePlayerScreen使用，用于保存观看历史
     */
    fun setOnTimeChangedListener(listener: (Long, Long) -> Unit) {
        externalTimeChangedListener = listener
    }

    // 🔥 新增：时间变化监听 - 对标原版FongMi_TV的onTimeChanged()
    private fun onTimeChanged(position: Long, duration: Long) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

        // 更新历史记录位置
        mHistory?.setPosition(position)
        mHistory?.setDuration(duration)

        // 🔥 修复：调用外部监听器，用于保存观看历史
        externalTimeChangedListener?.invoke(position, duration)

        // 🔥 关键逻辑：片尾自动跳过检查 - 对标原版FongMi_TV
        val ending = mHistory?.getEnding() ?: 0
        if (ending > 0 && duration > 0 && ending + position >= duration) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ENDING_SKIP] 片尾时间到达，自动播放下一集")
            VodFlowTracker.logFlowStep(currentFlowId, "ENDING_SKIP", "片尾自动跳过: ${formatTime(ending)}")

            // 触发下一集播放
            onNextEpisodeCallback?.invoke()
        }
    }

    // 🔥 新增：播放新剧集时自动跳过片头 - 对标原版FongMi_TV的setPosition()
    fun playWithOpeningSkip(position: Long = 0) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        val opening = mHistory?.getOpening() ?: 0

        if (opening > 0) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [OPENING_SKIP_PLAY] 新剧集播放，自动跳过片头: ${formatTime(opening)}")
            VodFlowTracker.logFlowStep(currentFlowId, "OPENING_SKIP_PLAY", "新剧集片头跳过: ${formatTime(opening)}")

            // 使用Players的片头跳过方法
            mPlayers?.setPositionWithOpeningSkip(position, opening)
        } else {
            // 没有片头设置，正常播放
            if (position > 0) {
                mPlayers?.seekTo(position)
            }
        }
    }
    
    // 🔥 完整修复：onOpening() - 正确设置片头时间并提供用户提示
    fun onOpening(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [OPENING_SET] 设置片头跳过时间")

        return safePlayerOperation("片头") { players ->
            val current = players.position
            val duration = players.duration

            if (current < 0 || duration < 0) {
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [OPENING_SET_ERROR] 播放器状态无效")
                return@safePlayerOperation "片头"
            }

            // 🔥 修复：移除60秒限制，允许设置任意位置为片头
            setOpening(current)
            val result = formatTime(current)  // 🔥 修复：直接使用current时间而不是从history获取

            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [OPENING_SET_SUCCESS] 片头时间设置为: $result")
            VodFlowTracker.logFlowStep(currentFlowId, "OPENING", "片头时间: $result")

            // 🔥 关键修复：启用片头片尾自动跳过监听
            enableOpeningEndingSkip()

            // 🔥 新增：用户提示
            showToast("片头设置成功：$result")

            result
        }
    }

    // 直接移植原版方法：onOpeningAdd()
    fun onOpeningAdd(): String {
        val currentOpening = mHistory?.getOpening() ?: 0
        setOpening(maxOf(0, currentOpening + 1000))
        return formatTime(mHistory?.getOpening() ?: 0)
    }

    // 直接移植原版方法：onOpeningSub()
    fun onOpeningSub(): String {
        val currentOpening = mHistory?.getOpening() ?: 0
        setOpening(maxOf(0, currentOpening - 1000))
        return formatTime(mHistory?.getOpening() ?: 0)
    }

    private fun setOpening(opening: Long) {
        mHistory?.setOpening(opening)
    }

    // 🔥 完整修复：onEnding() - 正确设置片尾时间，立即跳转下一集，并提供用户提示
    fun onEnding(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ENDING_SET] 设置片尾跳过时间")

        return safePlayerOperation("片尾") { players ->
            val current = players.position
            val duration = players.duration

            if (current < 0 || duration < 0) {
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ENDING_SET_ERROR] 播放器状态无效")
                return@safePlayerOperation "片尾"
            }

            // 🔥 修复：移除60秒限制，允许设置任意位置为片尾
            val endingTime = duration - current
            setEnding(endingTime)
            val result = formatTime(endingTime)  // 🔥 修复：直接使用endingTime而不是从history获取

            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ENDING_SET_SUCCESS] 片尾时间设置为: $result")
            VodFlowTracker.logFlowStep(currentFlowId, "ENDING", "片尾时间: $result")

            // 🔥 关键修复：启用片头片尾自动跳过监听
            enableOpeningEndingSkip()

            // 🔥 新增：用户提示
            showToast("片尾设置成功：$result")

            // 🔥 新增：设置片尾后立即跳转下一集
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ENDING_IMMEDIATE_NEXT] 片尾设置完成，立即播放下一集")
            onNextEpisodeCallback?.invoke()

            result
        }
    }
    
    // 直接移植原版方法：onEndingAdd()
    fun onEndingAdd(): String {
        val currentEnding = mHistory?.getEnding() ?: 0
        setEnding(maxOf(0, currentEnding + 1000))
        return formatTime(mHistory?.getEnding() ?: 0)
    }
    
    // 直接移植原版方法：onEndingSub()
    fun onEndingSub(): String {
        val currentEnding = mHistory?.getEnding() ?: 0
        setEnding(maxOf(0, currentEnding - 1000))
        return formatTime(mHistory?.getEnding() ?: 0)
    }
    
    private fun setEnding(ending: Long) {
        mHistory?.setEnding(ending)
    }
    

    
    // 直接移植原版方法：onChoose() - 播放器切换
    fun onPlayer(): String {
        _mPlayers?.choose(activity, "播放器选择")
        return "EXO"
    }

    // 🔥 完整移植原版方法：onReset() - 添加VOD_FLOW日志和实际逻辑
    fun onReset(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RESET] 播放器重置开始")

        onReset(isReplay())

        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RESET_SUCCESS] 播放器重置完成")
        VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_RESET", "播放器重置完成")
        return "刷新"
    }

    // 🔥 完整移植原版方法：onRefresh() - 添加VOD_FLOW日志
    fun onRefresh(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_REFRESH] 播放器刷新开始")

        onReset(false)

        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_REFRESH_SUCCESS] 播放器刷新完成")
        VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_REFRESH", "播放器刷新完成")
        return "刷新"
    }

    // 🔥 完整移植原版reset逻辑 - 直接移植VideoActivity.onReset()
    private fun onReset(replay: Boolean) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

        try {
            // 🔥 直接移植原版逻辑：停止播放器
            _mPlayers?.stop()
            _mPlayers?.clear()

            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [RESET_PLAYER_STOPPED] 播放器已停止")

            // 🔥 原版会触发重新获取播放地址的逻辑
            // 这里需要通过回调通知上层重新加载
            _onResetCallback?.invoke(replay)

        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [RESET_ERROR] 重置过程出错: ${e.message}")
        }
    }

    private fun isReplay(): Boolean {
        return Setting.getReset() == 1
    }

    // 🔥 完整移植原版方法：onResetToggle() - 添加VOD_FLOW日志
    fun onResetToggle(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

        val resetModes = arrayOf("刷新", "重载")
        val currentMode = Setting.getReset()
        val newMode = Math.abs(currentMode - 1)
        Setting.putReset(newMode)

        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [RESET_MODE_TOGGLE] 重置模式切换为: ${resetModes[newMode]}")
        VodFlowTracker.logFlowStep(currentFlowId, "RESET_MODE", "切换为: ${resetModes[newMode]}")

        return resetModes[newMode]
    }

    // 🔥 完整移植原版换源功能 - 添加VOD_FLOW日志和实际逻辑
    fun onChangeSource(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CHANGE_SOURCE] 换源操作开始")

        try {
            // 🔥 原版onChange逻辑：触发换源搜索
            _onChangeSourceCallback?.invoke()

            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CHANGE_SOURCE_SUCCESS] 换源操作完成")
            VodFlowTracker.logFlowStep(currentFlowId, "CHANGE_SOURCE", "换源操作完成")

            return "换源"
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [CHANGE_SOURCE_ERROR] 换源操作失败: ${e.message}")
            return "换源"
        }
    }

    // 🔥 添加回调接口设置方法，用于通知上层重新加载和换源
    fun setOnResetCallback(callback: (Boolean) -> Unit) {
        _onResetCallback = callback
    }

    fun setOnChangeSourceCallback(callback: () -> Unit) {
        _onChangeSourceCallback = callback
    }
    
    // 直接移植原版轨道选择功能 - 优化版本
    fun onTrack(trackType: Int): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        val defaultName = when (trackType) {
            1 -> "音轨"
            2 -> "视轨"
            3 -> "字幕"
            else -> "轨道"
        }

        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_TRACK] 轨道切换: $defaultName")

        return safePlayerOperation(defaultName) { players ->
            val key = players.key ?: return@safePlayerOperation defaultName
            val tracks = top.cywin.onetv.movie.bean.Track.find(key)

            // 找到指定类型的轨道
            val typeTracks = tracks.filter { it.type == trackType }
            if (typeTracks.isEmpty()) {
                return@safePlayerOperation defaultName
            }

            // 找到当前选中的轨道
            val currentIndex = typeTracks.indexOfFirst { it.isSelected }
            val nextIndex = if (currentIndex == -1) 0 else (currentIndex + 1) % typeTracks.size

            // 取消所有同类型轨道的选择
            typeTracks.forEach { it.setSelected(false) }

            // 选择下一个轨道
            val selectedTrack = typeTracks[nextIndex]
            selectedTrack.setSelected(true)

            // 应用轨道选择
            players.setTrack(listOf(selectedTrack))

            val trackName = selectedTrack.name ?: defaultName
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_TRACK_SUCCESS] 轨道切换成功: $trackName")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_TRACK", "轨道切换成功: $trackName")

            trackName
        }
    }

    // 🔥 完善轨道切换功能 - 添加详细的VOD_FLOW日志
    fun onTextTrack(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SUBTITLE_TRACK] 字幕轨道切换")
        val result = onTrack(3)
        VodFlowTracker.logFlowStep(currentFlowId, "SUBTITLE_TRACK", "字幕轨道: $result")
        return result
    }

    fun onAudioTrack(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [AUDIO_TRACK] 音轨切换")
        val result = onTrack(1)
        VodFlowTracker.logFlowStep(currentFlowId, "AUDIO_TRACK", "音轨: $result")
        return result
    }

    fun onVideoTrack(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [VIDEO_TRACK] 视频轨道切换")
        val result = onTrack(2)
        VodFlowTracker.logFlowStep(currentFlowId, "VIDEO_TRACK", "视频轨道: $result")
        return result
    }

    // 🔥 完整移植原版弹幕功能 - 添加VOD_FLOW日志和实际弹幕控制
    fun onDanmaku(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DANMAKU_TOGGLE] 弹幕开关切换")

        return safePlayerOperation("弹幕关") { players ->
            val isShow = !Setting.isDanmakuShow()
            Setting.putDanmakuShow(isShow)

            // 🔥 关键修复：实际控制弹幕显示/隐藏
            if (isShow) {
                // 显示弹幕：如果有弹幕数据，则显示
                if (players.haveDanmaku()) {
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DANMAKU_SHOW] 显示弹幕")
                    // 弹幕会通过DanPlayer自动显示，这里只需要设置状态
                } else {
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DANMAKU_NO_DATA] 无弹幕数据")
                }
            } else {
                // 隐藏弹幕
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DANMAKU_HIDE] 隐藏弹幕")
                // 弹幕会通过DanPlayer自动隐藏，这里只需要设置状态
            }

            val result = if (isShow) "弹幕开" else "弹幕关"
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DANMAKU_TOGGLE_SUCCESS] 弹幕状态: $result")
            VodFlowTracker.logFlowStep(currentFlowId, "DANMAKU", "弹幕状态: $result")

            result
        }
    }
    
    private fun formatTime(timeMs: Long): String {
        if (timeMs <= 0) return "片头"
        return _mPlayers?.stringToTime(timeMs) ?: "00:00"
    }

    // 播放器资源释放 - 添加VOD_FLOW日志
    fun release() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE] 开始释放播放器资源")

        try {
            _mPlayers?.release()
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_SUCCESS] 播放器资源释放成功")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_RELEASE", "播放器资源释放完成")
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_ERROR] 播放器资源释放异常", e)
        } finally {
            _mPlayers = null
            mHistory = null
            isInitialized = false
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_COMPLETE] 播放器状态重置完成")
        }
    }

    // 优化的播放器初始化检查
    fun ensureInitialized(): Boolean {
        return isInitialized && _mPlayers != null
    }

    // 安全的播放器操作包装
    private inline fun <T> safePlayerOperation(defaultValue: T, operation: (Players) -> T): T {
        return if (ensureInitialized()) {
            try {
                _mPlayers?.let { operation(it) } ?: defaultValue
            } catch (e: Exception) {
                defaultValue
            }
        } else {
            defaultValue
        }
    }

    /**
     * 🔥 处理播放中断 - 当播放器意外进入IDLE状态时调用
     */
    fun handlePlaybackInterruption() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_DETECTED] 检测到播放中断，尝试恢复播放")

        // 延迟3秒后尝试恢复播放，给网络缓冲一些时间
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                val players = _mPlayers
                if (players != null) {
                    // 🔥 修复：使用ExoPlayer的play()方法恢复播放，而不是start()
                    val exoPlayer = players.get()
                    if (exoPlayer != null) {
                        exoPlayer.play()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY] 播放恢复成功")
                    } else {
                        Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_FAILED] ExoPlayer为空，无法恢复播放")
                    }
                } else {
                    Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_FAILED] Players为空，无法恢复播放")
                }
            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_ERROR] 播放恢复失败: ${e.message}")
            }
        }, 3000) // 延迟3秒
    }

    // 🔥 新增：Toast提示方法
    private fun showToast(message: String) {
        Handler(Looper.getMainLooper()).post {
            Toast.makeText(activity, message, Toast.LENGTH_SHORT).show()
        }
    }
}

@Composable
fun rememberVideoController(activity: Activity): VideoController {
    return remember(activity) { VideoController(activity) }
}
