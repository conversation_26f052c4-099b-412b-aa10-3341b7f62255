package top.cywin.onetv.movie.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.util.Log
import top.cywin.onetv.movie.player.VideoController
import top.cywin.onetv.movie.ui.model.Episode
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * 播放器控制栏 - 直接移植原版控制栏布局
 * 基于：FongMi_TV/src/leanback/res/layout/vod_view_control_vod.xml
 */

/**
 * 🔥 顶部电影信息显示 - 全屏模式下显示当前集数信息
 * 基于原版VideoActivity的顶部信息显示逻辑
 */
@Composable
fun PlayerTopInfo(
    movieTitle: String,
    currentEpisode: String,
    totalEpisodes: Int,
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isVisible,
        modifier = modifier,
        enter = slideInVertically(
            initialOffsetY = { -it }, // 从顶部滑入
            animationSpec = tween(300)
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            targetOffsetY = { -it }, // 向顶部滑出
            animationSpec = tween(300)
        ) + fadeOut(animationSpec = tween(300))
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            Color.Black.copy(alpha = 0.8f),
                            Color.Transparent
                        )
                    )
                )
                .padding(start = 16.dp, top = 32.dp, end = 16.dp, bottom = 16.dp)
        ) {
            Column {
                // 电影标题
                Text(
                    text = movieTitle,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 集数信息 - 例如："第01集 共24集"
                Text(
                    text = if (totalEpisodes > 1) {
                        "${currentEpisode} 共${totalEpisodes}集"
                    } else {
                        currentEpisode
                    },
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    }
}
@Composable
fun PlayerControls(
    controller: VideoController,
    episodes: List<Episode>,
    currentEpisodeIndex: Int,
    isVisible: Boolean,
    onEpisodeSelect: (Int) -> Unit,
    onRefresh: () -> Unit,
    onChangeSource: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 🔥 修复：底部控制栏的正确动画 - 从底部滑入/滑出
    AnimatedVisibility(
        visible = isVisible,
        modifier = modifier, // 🔥 关键修复：将对齐修饰符应用到AnimatedVisibility上
        enter = slideInVertically(
            initialOffsetY = { it }, // 从底部滑入
            animationSpec = tween(300)
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            targetOffsetY = { it }, // 向底部滑出
            animationSpec = tween(300)
        ) + fadeOut(animationSpec = tween(300))
    ) {
        // 🔥 修复：确保控制栏在底部显示，移除错误的verticalArrangement
        Column(
            modifier = Modifier // 🔥 关键修复：Column使用默认Modifier，对齐由AnimatedVisibility处理
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Black.copy(alpha = 0.8f)
                        )
                    ),
                    shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
                )
                .padding(start = 16.dp, top = 18.dp, end = 16.dp, bottom = 8.dp)
            // 🔥 关键修复：移除verticalArrangement.Bottom，让Column正常排列内容
        ) {
            // 移植原版HorizontalScrollView + LinearLayout结构
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState()),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 直接移植原版14个控制按钮
                ControlButtonRow(
                    controller = controller,
                    episodes = episodes,
                    currentEpisodeIndex = currentEpisodeIndex,
                    onEpisodeSelect = onEpisodeSelect,
                    onRefresh = onRefresh,
                    onChangeSource = onChangeSource
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 直接移植原版进度条：android:id="@+id/seek" (CustomSeekView)
            PlayerSeekBar(controller = controller)
        }
    }
}

@Composable
private fun ControlButtonRow(
    controller: VideoController,
    episodes: List<Episode>,
    currentEpisodeIndex: Int,
    onEpisodeSelect: (Int) -> Unit,
    onRefresh: () -> Unit,
    onChangeSource: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 所有14个原版控制按钮的状态
    var decodeText by remember { mutableStateOf("硬解") }
    var speedText by remember { mutableStateOf("1.00") }
    var scaleText by remember { mutableStateOf("原始") }
    var textTrackText by remember { mutableStateOf("字幕") }
    var audioTrackText by remember { mutableStateOf("音轨") }
    var videoTrackText by remember { mutableStateOf("视轨") }
    var openingText by remember { mutableStateOf("片头") }
    var endingText by remember { mutableStateOf("片尾") }
    var loopText by remember { mutableStateOf("循环") }
    var danmakuText by remember { mutableStateOf("弹幕") }
    var resetText by remember { mutableStateOf("刷新") }
    var changeSourceText by remember { mutableStateOf("换源") }
    var playerText by remember { mutableStateOf("EXO") }

    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 按原版顺序排列所有14个按钮
        // 下集按钮
        ControlButton(
            text = "下集",
            onClick = {
                if (currentEpisodeIndex < episodes.size - 1) {
                    onEpisodeSelect(currentEpisodeIndex + 1)
                }
            }
        )

        // 上集按钮
        ControlButton(
            text = "上集",
            onClick = {
                if (currentEpisodeIndex > 0) {
                    onEpisodeSelect(currentEpisodeIndex - 1)
                }
            }
        )

        // 刷新按钮
        ControlButton(
            text = resetText,
            onClick = {
                resetText = controller.onResetToggle()
                onRefresh()
            }
        )

        // 换源按钮
        ControlButton(
            text = changeSourceText,
            onClick = {
                changeSourceText = controller.onChangeSource()
                onChangeSource()
            }
        )

        // 播放器切换按钮
        ControlButton(
            text = playerText,
            onClick = { playerText = controller.onPlayer() }
        )

        // 解码模式按钮
        ControlButton(
            text = decodeText,
            onClick = { decodeText = controller.onDecode() }
        )

        // 播放速度按钮
        ControlButton(
            text = speedText,
            onClick = { speedText = controller.onSpeed() }
        )

        // 缩放模式按钮
        ControlButton(
            text = scaleText,
            onClick = { scaleText = controller.onScale() }
        )

        // 字幕轨按钮
        ControlButton(
            text = textTrackText,
            onClick = { textTrackText = controller.onTextTrack() }
        )

        // 音轨按钮
        ControlButton(
            text = audioTrackText,
            onClick = { audioTrackText = controller.onAudioTrack() }
        )

        // 视频轨按钮
        ControlButton(
            text = videoTrackText,
            onClick = { videoTrackText = controller.onVideoTrack() }
        )

        // 片头按钮
        ControlButton(
            text = openingText,
            onClick = { openingText = controller.onOpening() }
        )

        // 片尾按钮
        ControlButton(
            text = endingText,
            onClick = { endingText = controller.onEnding() }
        )

        // 循环播放按钮
        ControlButton(
            text = loopText,
            onClick = { loopText = controller.onLoop() }
        )

        // 弹幕按钮
        ControlButton(
            text = danmakuText,
            onClick = { danmakuText = controller.onDanmaku() }
        )
    }
}

// 直接移植原版TextView样式
@Composable
private fun ControlButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier
            .clip(RoundedCornerShape(4.dp))
            .background(Color.Transparent)
            .clickable { onClick() }
            .padding(horizontal = 8.dp, vertical = 4.dp),
        color = Color.White,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal
    )
}

// 🔥 完整移植原版CustomSeekView - 支持拖拽跳转功能
@Composable
private fun PlayerSeekBar(
    controller: VideoController,
    modifier: Modifier = Modifier
) {
    // 播放器状态
    var currentTime by remember { mutableStateOf("00:00") }
    var totalTime by remember { mutableStateOf("00:00") }
    var progress by remember { mutableStateOf(0f) }
    var duration by remember { mutableStateOf(0L) }
    var isDragging by remember { mutableStateOf(false) }
    var dragProgress by remember { mutableStateOf(0f) }

    // 🔥 添加VOD_FLOW日志跟踪
    val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

    // 优化的播放进度更新 - 减少不必要的计算
    LaunchedEffect(controller) {
        while (true) {
            if (controller.isInitialized && !isDragging) {
                val players = controller.mPlayers
                if (players != null) {
                    try {
                        val position = players.position
                        val dur = players.duration

                        // 只在有效时间时更新UI
                        if (position >= 0 && dur > 0) {
                            currentTime = players.stringToTime(position)
                            totalTime = players.durationTime
                            progress = position.toFloat() / dur.toFloat()
                            duration = dur
                        }
                    } catch (e: Exception) {
                        // 静默处理异常，避免崩溃
                    }
                }
            }
            kotlinx.coroutines.delay(1000) // 保持1秒更新频率
        }
    }

    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 当前时间
        Text(
            text = if (isDragging) {
                // 🔥 拖拽时显示预览时间
                controller.mPlayers?.stringToTime((dragProgress * duration).toLong()) ?: currentTime
            } else {
                currentTime
            },
            color = Color.White,
            fontSize = 12.sp
        )

        // 🔥 完整拖拽进度条 - 适用于TV端遥控器操作
        Box(
            modifier = Modifier
                .weight(1f)
                .height(24.dp) // 增加高度便于拖拽操作
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            isDragging = true
                            val newProgress = (offset.x / size.width).coerceIn(0f, 1f)
                            dragProgress = newProgress

                            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEEK_START] 开始拖拽进度条: ${(newProgress * 100).toInt()}%")
                            VodFlowTracker.logFlowStep(currentFlowId, "SEEK_START", "开始拖拽进度条")
                        },
                        onDrag = { change, _ ->
                            val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                            dragProgress = newProgress

                            // 🔥 实时显示拖拽位置的时间
                            val seekTime = (dragProgress * duration).toLong()
                            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEEK_DRAG] 拖拽到: ${controller.mPlayers?.stringToTime(seekTime) ?: "00:00"}")
                        },
                        onDragEnd = {
                            // 🔥 拖拽结束时执行跳转
                            val seekPosition = (dragProgress * duration).toLong()
                            controller.mPlayers?.seekTo(seekPosition)

                            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEEK_SUCCESS] 跳转到位置: ${controller.mPlayers?.stringToTime(seekPosition) ?: "00:00"}")
                            VodFlowTracker.logFlowStep(currentFlowId, "SEEK", "跳转到位置: ${controller.mPlayers?.stringToTime(seekPosition) ?: "00:00"}")

                            isDragging = false
                        }
                    )
                }
                .clickable {
                    // 🔥 支持点击跳转（适用于鼠标操作）
                    // 这个功能作为拖拽的补充，但主要还是拖拽功能
                }
        ) {
            // 进度条背景
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp) // 稍微增加厚度
                    .align(Alignment.Center)
                    .background(Color.Gray.copy(alpha = 0.4f), RoundedCornerShape(3.dp))
            )

            // 进度条前景 - 显示当前播放进度或拖拽进度
            val displayProgress = if (isDragging) dragProgress else progress
            Box(
                modifier = Modifier
                    .fillMaxWidth(displayProgress.coerceIn(0f, 1f))
                    .height(6.dp)
                    .align(Alignment.CenterStart)
                    .background(Color.White, RoundedCornerShape(3.dp))
            )

            // 🔥 拖拽指示器 - 显示当前拖拽位置
            if (isDragging || progress > 0f) {
                val thumbProgress = if (isDragging) dragProgress else progress

                // 使用BoxWithConstraints来获取容器宽度
                BoxWithConstraints(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    val containerWidth = maxWidth
                    val thumbSize = 16.dp
                    val thumbOffset = thumbProgress * (containerWidth - thumbSize).value

                    Box(
                        modifier = Modifier
                            .size(thumbSize)
                            .offset(x = thumbOffset.dp)
                            .align(Alignment.CenterStart)
                            .background(Color.White, CircleShape)
                            .border(2.dp, Color.Black.copy(alpha = 0.3f), CircleShape)
                    )
                }
            }
        }

        // 总时间
        Text(
            text = totalTime,
            color = Color.White,
            fontSize = 12.sp
        )
    }
}