package top.cywin.onetv.movie.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.util.Log

@Composable
fun SiteSelector(
    sites: List<top.cywin.onetv.movie.bean.Site>,
    selectedSite: top.cywin.onetv.movie.bean.Site?,
    onSiteSelected: (top.cywin.onetv.movie.bean.Site) -> Unit,
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 🔥 添加VOD_FLOW日志跟踪
    Log.d("VOD_FLOW", "[SITE_SELECTOR] 显示站点选择器，站点数量: ${sites.size}")
    Log.d("VOD_FLOW", "[SITE_SELECTOR] 当前选中站点: ${selectedSite?.name ?: "无"}")
    Log.d("VOD_FLOW", "[SITE_SELECTOR] 布局: 5列多行，支持滚动显示")
    Dialog(
        onDismissRequest = {
            Log.d("VOD_FLOW", "[SITE_SELECTOR] 用户关闭站点选择器")
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true, // 🔥 确保点击外部可以关闭
            usePlatformDefaultWidth = false
        )
    ) {
        // 🔥 修复：使用Box但不填满整个屏幕，避免阻止外部点击事件
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) {
                    // 🔥 修复：点击空白区域关闭弹窗
                    Log.d("VOD_FLOW", "[SITE_SELECTOR] 点击外部区域关闭弹窗")
                    onDismiss()
                }
        ) {
            Card(
                modifier = modifier
                    .fillMaxWidth(0.95f)
                    .fillMaxHeight(0.7f) // 🔥 修复：调整高度，为对齐分隔线留出空间
                    .align(Alignment.Center) // 🔥 修复：改为居中对齐，便于调整位置
                    .offset(y = 40.dp) // 🔥 修复：向下偏移，对齐分类标签下方的分隔线
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    // 🔥 修复：改为5列布局，支持10行显示，超出部分可滚动
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(5), // 🔥 修改为5列
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(6.dp), // 🔥 减少内容边距
                        horizontalArrangement = Arrangement.spacedBy(6.dp), // 🔥 调整水平间距
                        verticalArrangement = Arrangement.spacedBy(4.dp), // 🔥 减少垂直间距，更紧凑
                        userScrollEnabled = true // 🔥 确保可以滚动
                    ) {
                        items(sites) { site ->
                            SiteGridItem(
                                site = site,
                                isSelected = selectedSite?.key == site.key,
                                onClick = {
                                    if (selectedSite?.key != site.key) {
                                        Log.d("VOD_FLOW", "[SITE_SELECT] 用户选择站点: ${site.name}")
                                        onSiteSelected(site)
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SiteGridItem(
    site: top.cywin.onetv.movie.bean.Site,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(3.0f) // 🔥 修复：进一步减少高度，使标签更紧凑
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        shape = RoundedCornerShape(6.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(1.dp), // 🔥 修复：进一步减少内边距，最大化利用空间
            contentAlignment = Alignment.Center
        ) {
            // 🔥 修复：改为Row布局，将"√"图标放在站点名字右侧，实现单行显示
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = (site.name ?: "Unknown").take(8), // 🔥 修复：增加显示字符数，5列布局有更多空间
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 12.sp // 🔥 修复：稍微减小字体，适应更紧凑的布局
                    ),
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                    modifier = Modifier.weight(1f) // 🔥 让文本占据大部分空间
                )

                // 🔥 修复：将"√"图标放在文本右侧，只在选中时显示
                if (isSelected) {
                    Spacer(modifier = Modifier.width(2.dp))
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(12.dp) // 🔥 稍微减小图标尺寸，节省空间
                    )
                }
            }
        }
    }
}