package top.cywin.onetv.movie.api.loader;

import android.content.Context;

import top.cywin.onetv.movie.App;
import top.cywin.onetv.movie.utils.UrlUtil;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderNull;
import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.Path;
import com.github.catvod.utils.Util;

import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import dalvik.system.DexClassLoader;

public class JarLoader {

    private final ConcurrentHashMap<String, DexClassLoader> loaders;
    private final ConcurrentHashMap<String, Method> methods;
    private final ConcurrentHashMap<String, Spider> spiders;
    private String recent;

    public JarLoader() {
        loaders = new ConcurrentHashMap<>();
        methods = new ConcurrentHashMap<>();
        spiders = new ConcurrentHashMap<>();
    }

    public void clear() {
        for (Spider spider : spiders.values())
            App.execute(spider::destroy);
        loaders.clear();
        methods.clear();
        spiders.clear();
    }

    public void setRecent(String recent) {
        this.recent = recent;
    }

    private void load(String key, File file) {
        android.util.Log.d("ONETV_JAR_LOADER", "🔄 加载JAR文件: key=" + key + ", file=" + file.getAbsolutePath());
        android.util.Log.d("ONETV_JAR_LOADER", "📊 文件存在: " + file.exists() + ", 大小: " + file.length() + " bytes");

        if (!file.setReadOnly()) {
            android.util.Log.e("ONETV_JAR_LOADER", "❌ 设置文件只读失败");
            return;
        }

        loaders.put(key, dex(file));
        android.util.Log.d("ONETV_JAR_LOADER", "✅ ClassLoader创建成功");

        invokeInit(key);
        android.util.Log.d("ONETV_JAR_LOADER", "✅ Init调用完成");

        putProxy(key);
        android.util.Log.d("ONETV_JAR_LOADER", "✅ Proxy设置完成");
    }

    private DexClassLoader dex(File file) {
        return new DexClassLoader(file.getAbsolutePath(), Path.jar().getAbsolutePath(), null,
                App.get().getClassLoader());
    }

    private void invokeInit(String key) {
        try {
            Class<?> clz = loaders.get(key).loadClass("com.github.catvod.spider.Init");
            Method method = clz.getMethod("init", Context.class);
            method.invoke(clz, App.get());
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void putProxy(String key) {
        try {
            Class<?> clz = loaders.get(key).loadClass("com.github.catvod.spider.Proxy");
            Method method = clz.getMethod("proxy", Map.class);
            methods.put(key, method);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private File download(String url) {
        try {
            return Path.write(Path.jar(url), OkHttp.bytes(url));
        } catch (Exception e) {
            return Path.jar(url);
        }
    }

    public synchronized void parseJar(String key, String jar) {
        android.util.Log.d("ONETV_JAR_LOADER", "🔄 解析JAR: key=" + key + ", jar=" + jar);

        if (loaders.containsKey(key)) {
            android.util.Log.d("ONETV_JAR_LOADER", "✅ JAR已存在，跳过解析");
            return;
        }

        String[] texts = jar.split(";md5;");
        String md5 = texts.length > 1 ? texts[1].trim() : "";
        if (md5.startsWith("http"))
            md5 = OkHttp.string(md5).trim();
        jar = texts[0];

        android.util.Log.d("ONETV_JAR_LOADER", "📊 JAR URL: " + jar + ", MD5: " + md5);

        if (!md5.isEmpty() && Util.equals(jar, md5)) {
            android.util.Log.d("ONETV_JAR_LOADER", "🔄 使用缓存JAR");
            load(key, Path.jar(jar));
        } else if (jar.startsWith("http")) {
            android.util.Log.d("ONETV_JAR_LOADER", "🔄 下载HTTP JAR");
            File jarFile = download(jar);
            if (jarFile != null && jarFile.exists()) {
                load(key, jarFile);
            } else {
                android.util.Log.e("ONETV_JAR_LOADER", "❌ JAR下载失败: " + jar);
                // 🔥 修复：下载失败时使用本地默认JAR
                load(key, Path.jar("assets://jar/spider.jar"));
            }
        } else if (jar.startsWith("file")) {
            android.util.Log.d("ONETV_JAR_LOADER", "🔄 加载本地JAR");
            load(key, Path.local(jar));
        } else if (jar.startsWith("assets")) {
            android.util.Log.d("ONETV_JAR_LOADER", "🔄 处理assets JAR");
            parseJar(key, UrlUtil.convert(jar));
        }
    }

    public DexClassLoader dex(String jar) {
        try {
            String jaKey = Util.md5(jar);
            if (!loaders.containsKey(jaKey))
                parseJar(jaKey, jar);
            return loaders.get(jaKey);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

    public Spider getSpider(String key, String api, String ext, String jar) {
        // 🔥 获取当前FlowID用于完整的日志跟踪
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "JAR_GET_SPIDER_START",
                "JarLoader开始获取Spider: key=" + key + ", api=" + api);
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_START] JarLoader开始获取Spider: key="
                + key + ", api=" + api);

        // 🔥 关键修复：参数验证
        if (key == null || key.trim().isEmpty()) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_INVALID_KEY", "key参数无效，返回SpiderNull");
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_INVALID_KEY] key参数无效，返回SpiderNull");
            return new SpiderNull();
        }

        if (api == null || !api.startsWith("csp_")) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_INVALID_API", "api参数无效: " + api + "，返回SpiderNull");
            android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_INVALID_API] api参数无效: "
                    + api + "，返回SpiderNull");
            return new SpiderNull();
        }

        // 🔥 关键修复：移除过于严格的jar空值检查，与原版FongMi_TV保持一致
        // 原版FongMi_TV允许jar为空，在parseJar中进行处理
        if (jar == null) {
            jar = ""; // 将null转换为空字符串，避免后续处理出错
        }

        String jaKey = Util.md5(jar);
        String spKey = jaKey + key;
        Spider spider = null; // 🔥 修复：在方法级别定义spider变量

        try {

            // 🔥 检查缓存
            if (spiders.containsKey(spKey)) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "JAR_GET_SPIDER_CACHE",
                        "从缓存获取Spider");
                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_CACHE] 从缓存获取Spider");
                return spiders.get(spKey);
            }

            // 🔥 关键修复：确保JAR已加载，并验证加载结果
            if (!loaders.containsKey(jaKey)) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "JAR_GET_SPIDER_PARSE",
                        "解析JAR文件: " + jaKey);
                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_PARSE] 解析JAR文件: " + jaKey);
                parseJar(jaKey, jar);

                // 🔥 关键修复：验证parseJar是否成功
                if (!loaders.containsKey(jaKey)) {
                    top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                            "JAR_GET_SPIDER_PARSE_FAILED", "JAR解析失败，ClassLoader不存在，返回SpiderNull");
                    android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId
                            + "] [JAR_GET_SPIDER_PARSE_FAILED] JAR解析失败，ClassLoader不存在，返回SpiderNull");
                    return new SpiderNull();
                }
            }

            // 🔥 关键修复：验证ClassLoader是否有效
            DexClassLoader classLoader = loaders.get(jaKey);
            if (classLoader == null) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_NO_LOADER", "ClassLoader为null，返回SpiderNull");
                android.util.Log.e("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_NO_LOADER] ClassLoader为null，返回SpiderNull");
                return new SpiderNull();
            }

            // 🔥 关键修复：安全的类名构建
            String className = null;
            try {
                String[] apiParts = api.split("csp_");
                if (apiParts.length < 2 || apiParts[1].trim().isEmpty()) {
                    top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                            "JAR_GET_SPIDER_INVALID_CLASS", "无法从api构建类名: " + api + "，返回SpiderNull");
                    android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId
                            + "] [JAR_GET_SPIDER_INVALID_CLASS] 无法从api构建类名: " + api + "，返回SpiderNull");
                    return new SpiderNull();
                }
                className = "com.github.catvod.spider." + apiParts[1];
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_CLASS_NAME", "构建Spider类名: " + className);
                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_CLASS_NAME] 尝试加载Spider类: " + className);
                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_PARAMS] Spider参数: key=" + key + ", ext长度="
                                + (ext != null ? ext.length() : 0) + ", jar="
                                + jar.substring(0, Math.min(50, jar.length())) + "...");
            } catch (Exception e) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_CLASS_NAME_ERROR", "构建类名失败: " + e.getMessage() + "，返回SpiderNull");
                android.util.Log.e("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_CLASS_NAME_ERROR] 构建类名失败，返回SpiderNull", e);
                return new SpiderNull();
            }

            // 🔥 关键修复：安全的类加载和实例化
            try {
                Class<?> spiderClass = classLoader.loadClass(className);
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_CLASS_LOADED", "Spider类加载成功: " + spiderClass.getName());
                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId
                        + "] [JAR_GET_SPIDER_CLASS_LOADED] Spider类加载成功: " + spiderClass.getName());

                Object spiderInstance = spiderClass.newInstance();
                if (!(spiderInstance instanceof Spider)) {
                    top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                            "JAR_GET_SPIDER_NOT_SPIDER", "实例化的对象不是Spider类型，返回SpiderNull");
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_NOT_SPIDER] 实例化的对象不是Spider类型，返回SpiderNull");
                    return new SpiderNull();
                }

                spider = (Spider) spiderInstance;
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_INSTANCE_SUCCESS", "Spider实例化成功: " + spider.getClass().getName());
                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId
                        + "] [JAR_GET_SPIDER_INSTANCE_SUCCESS] Spider实例化成功: " + spider.getClass().getName());
            } catch (ClassNotFoundException e) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                        "JAR_GET_SPIDER_CLASS_NOT_FOUND", "Spider类未找到: " + className + "，返回SpiderNull");
                android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId
                        + "] [JAR_GET_SPIDER_CLASS_NOT_FOUND] Spider类未找到: " + className + "，返回SpiderNull", e);
                return new SpiderNull();
            }
        } catch (InstantiationException e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_INSTANTIATION_ERROR", "Spider实例化失败: " + e.getMessage() + "，返回SpiderNull");
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_INSTANTIATION_ERROR] Spider实例化失败，返回SpiderNull",
                    e);
            return new SpiderNull();
        } catch (IllegalAccessException e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_ACCESS_ERROR", "Spider访问权限错误: " + e.getMessage() + "，返回SpiderNull");
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_ACCESS_ERROR] Spider访问权限错误，返回SpiderNull", e);
            return new SpiderNull();
        } catch (Throwable e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_LOAD_ERROR",
                    "Spider加载失败: " + e.getClass().getSimpleName() + " - " + e.getMessage() + "，返回SpiderNull");
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_LOAD_ERROR] Spider加载失败，返回SpiderNull", e);
            return new SpiderNull();
        }

        // 🔥 关键修复：安全的Spider初始化
        try {
            spider.init(App.get(), ext);
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_INIT_SUCCESS", "Spider初始化完成: " + key);
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_INIT_SUCCESS] Spider初始化完成: " + key);
        } catch (Throwable e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_INIT_ERROR",
                    "Spider初始化失败: " + e.getClass().getSimpleName() + " - " + e.getMessage() + "，返回SpiderNull");
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_INIT_ERROR] Spider初始化失败，返回SpiderNull", e);
            return new SpiderNull();
        }

        // 🔥 关键修复：安全的Spider监控包装器创建
        try {
            Spider monitoredSpider = new SpiderMonitor(spider, key);
            spiders.put(spKey, monitoredSpider);
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "JAR_GET_SPIDER_SUCCESS",
                    "JarLoader获取Spider成功: " + monitoredSpider.getClass().getSimpleName());
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_SUCCESS] Spider监控包装器创建成功，返回实例");
            return monitoredSpider;
        } catch (Throwable e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "JAR_GET_SPIDER_MONITOR_ERROR",
                    "Spider监控包装器创建失败: " + e.getClass().getSimpleName() + " - " + e.getMessage() + "，返回原始Spider");
            android.util.Log.w("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [JAR_GET_SPIDER_MONITOR_ERROR] Spider监控包装器创建失败，返回原始Spider", e);
            // 如果监控包装器创建失败，至少返回原始Spider
            spiders.put(spKey, spider);
            return spider;
        }
    }

    public JSONObject jsonExt(String key, LinkedHashMap<String, String> jxs, String url) throws Throwable {
        Class<?> clz = loaders.get(recent).loadClass("com.github.catvod.parser.Json" + key);
        Method method = clz.getMethod("parse", LinkedHashMap.class, String.class);
        return (JSONObject) method.invoke(null, jxs, url);
    }

    public JSONObject jsonExtMix(String flag, String key, String name,
            LinkedHashMap<String, HashMap<String, String>> jxs, String url) throws Throwable {
        Class<?> clz = loaders.get(recent).loadClass("com.github.catvod.parser.Mix" + key);
        Method method = clz.getMethod("parse", LinkedHashMap.class, String.class, String.class, String.class);
        return (JSONObject) method.invoke(null, jxs, name, flag, url);
    }

    public Object[] proxyInvoke(Map<String, String> params) {
        Object[] result = proxyInvoke(methods.get(recent), params);
        return result != null ? result : tryOthers(params);
    }

    private Object[] tryOthers(Map<String, String> params) {
        for (Map.Entry<String, Method> entry : methods.entrySet()) {
            if (entry.getKey().equals(recent))
                continue;
            Object[] result = proxyInvoke(entry.getValue(), params);
            if (result != null)
                return result;
        }
        return null;
    }

    private Object[] proxyInvoke(Method method, Map<String, String> params) {
        try {
            return (Object[]) method.invoke(null, params);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }
}
