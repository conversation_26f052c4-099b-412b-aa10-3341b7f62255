package top.cywin.onetv.movie.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import top.cywin.onetv.movie.ui.model.VodConfigUrl
import top.cywin.onetv.movie.MovieApp
import android.util.Log

/**
 * OneTV Movie线路选择器组件 - 对话框形式，采用与站点选择相同的设计风格
 */
@Composable
fun RouteSelector(
    routes: List<VodConfigUrl>,
    selectedRoute: VodConfigUrl?,
    onRouteSelected: (VodConfigUrl) -> Unit,
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Log.d("VOD_FLOW", "[ROUTE_SELECTOR] 渲染线路选择器，线路数量: ${routes.size}")

    Dialog(
        onDismissRequest = {
            Log.d("VOD_FLOW", "[ROUTE_SELECTOR] 用户关闭线路选择器")
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        // 🔥 修复：使用Box布局支持外部点击关闭和位置调整
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) {
                    // 🔥 修复：点击空白区域关闭弹窗
                    Log.d("VOD_FLOW", "[ROUTE_SELECTOR] 点击外部区域关闭弹窗")
                    onDismiss()
                }
        ) {
            Card(
                modifier = modifier
                    .fillMaxWidth(0.42f) // 🔥 修复：调整宽度为原来的1/2（从0.85f改为0.42f）
                    .fillMaxHeight(0.75f) // 🔥 修复：设置合理的高度，确保有足够空间显示线路并支持滚动
                    .align(Alignment.Center) // 🔥 修复：改为居中对齐，便于调整位置
                    .offset(y = 40.dp) // 🔥 修复：向下偏移，对齐分类标签下方的分隔线
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // ✅ 注释掉标题栏，按照用户要求采用与站点选择相同的设计风格
                /*
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "选择线路",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )

                    // ✅ 注释掉右上角X按钮，按照用户要求
                    /*
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    */
                }

                Spacer(modifier = Modifier.height(16.dp))
                */

                // 🔥 修复：使用1列多行布局的线路列表，支持垂直滚动
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f) // 🔥 使用weight确保占用剩余空间，这是关键
                        .heightIn(min = 200.dp), // 🔥 修复：设置最小高度，确保有足够空间滚动
                    contentPadding = PaddingValues(vertical = 8.dp, horizontal = 4.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    userScrollEnabled = true // 🔥 明确启用用户滚动
                ) {
                    items(routes) { route ->
                        RouteListItem(
                            route = route,
                            isSelected = selectedRoute?.url == route.url, // 🔥 修复：通过URL比较而不是对象引用
                            onClick = {
                                Log.d("VOD_FLOW", "[ROUTE_SELECT] 用户选择线路: ${route.name}")
                                onRouteSelected(route)
                                // 🔥 修复：选择线路后立即关闭弹窗
                                onDismiss()
                            }
                        )
                    }
                }
            }
        }
    }
}
}

/**
 * 线路列表项组件 - 1列布局，采用与站点选择相同的设计风格
 */
@Composable
private fun RouteListItem(
    route: VodConfigUrl,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 8.dp), // 🔥 修复：减少padding，使标签更紧凑
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🔥 修复：线路名称和选中标记放在同一行
            Text(
                text = route.name.take(20), // 🔥 修复：限制显示长度，避免过长
                style = MaterialTheme.typography.bodyMedium, // 🔥 修复：使用更小的字体
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f) // 🔥 修复：让文本占据大部分空间
            )

            // 🔥 修复：将选中标记放在线路名字的右侧
            if (isSelected) {
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "已选择",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp) // 🔥 修复：减小图标大小
                )
            }
        }
    }
}

@Composable
private fun RouteChip(
    route: VodConfigUrl,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = route.name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        selected = isSelected,
        leadingIcon = if (isSelected) {
            {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
            }
        } else null
    )
}

@Composable
fun RouteDropdown(
    routes: List<VodConfigUrl>,
    selectedRoute: VodConfigUrl?,
    onRouteSelected: (VodConfigUrl) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = selectedRoute?.name ?: "选择线路",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )
            Icon(
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = null
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.fillMaxWidth()
        ) {
            routes.forEach { route ->
                DropdownMenuItem(
                    text = { Text(route.name) },
                    onClick = {
                        onRouteSelected(route)
                        expanded = false
                    },
                    leadingIcon = if (selectedRoute == route) {
                        {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null
                            )
                        }
                    } else null
                )
            }
        }
    }
}
