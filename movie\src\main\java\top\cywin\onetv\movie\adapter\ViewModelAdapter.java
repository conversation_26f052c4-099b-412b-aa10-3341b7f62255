package top.cywin.onetv.movie.adapter;

import android.util.Log;
import java.util.ArrayList;
import java.util.List;
import top.cywin.onetv.movie.ui.model.*;

/**
 * ViewModel适配器 - 纯粹的数据转换器
 * 只负责FongMi_TV数据模型与Compose UI数据模型的转换
 */
public class ViewModelAdapter {

    private static final String TAG = "ViewModelAdapter";

    public ViewModelAdapter() {
        Log.d(TAG, "🏗️ ViewModelAdapter 初始化完成");
    }

    /**
     * 转换FongMi_TV的Vod为Compose UI的Movie
     */
    public static MovieItem convertVodToMovie(top.cywin.onetv.movie.bean.Vod vod) {
        if (vod == null)
            return null;

        // 🔥 核心修复：统一所有电影的vodId处理逻辑
        // 按照原版FongMi_TV逻辑，所有需要搜索的电影都使用msearch:home
        String vodId = vod.getVodId();

        // 🔧 关键修复：如果vodId为空或无效，统一设置为msearch:home触发搜索模式
        if (vodId == null || vodId.trim().isEmpty()) {
            vodId = "msearch:home";
            android.util.Log.d("ONETV_VOD_CONVERT", "🔧 [修复逻辑] 空vodId已修复: " + vod.getVodName() + " -> msearch:home");
        }

        // 🔧 调试日志：检查站点信息和vodId修复情况
        String siteKey = vod.getSite() != null ? vod.getSite().getKey() : "";
        String siteName = vod.getSite() != null ? vod.getSite().getName() : "未知站点";
        android.util.Log.d("ONETV_VOD_CONVERT", "🔄 转换Vod: " + vod.getVodName() + " -> vodId=" + vodId + ", siteKey="
                + siteKey + ", siteName=" + siteName);

        return new MovieItem(
                vodId,
                vod.getVodName(),
                vod.getVodPic(),
                vod.getVodRemarks(),
                vod.getVodYear(),
                vod.getVodArea(),
                vod.getVodDirector(),
                vod.getVodActor(),
                vod.getVodContent(),
                vod.getVodPlayFrom() != null ? vod.getVodPlayFrom() : "", // 🎬 添加播放来源字段
                siteKey,
                false, // isCollected - 需要单独查询
                0f, // watchProgress - 需要单独查询
                0L // lastWatchTime - 需要单独查询
        );
    }

    /**
     * 转换FongMi_TV的Site为Compose UI的SiteInfo
     */
    public static SiteInfo convertSiteToSiteInfo(top.cywin.onetv.movie.bean.Site site) {
        if (site == null)
            return null;

        return new SiteInfo(
                site.getKey(),
                site.getName(),
                site.getApi(),
                site.getSearchable() == 1,
                site.getPlayable() == 1,
                false // isActive - 需要单独判断
        );
    }

    /**
     * 转换FongMi_TV的Class为Compose UI的Category
     */
    public static CategoryInfo convertClassToCategory(top.cywin.onetv.movie.bean.Class clazz) {
        if (clazz == null)
            return null;

        return new CategoryInfo(
                clazz.getTypeId(),
                clazz.getTypeName(),
                clazz.getTypeFlag(),
                false // isSelected - 需要单独设置
        );
    }

    /**
     * 转换FongMi_TV的Result为Compose UI的SearchResult
     */
    public static SearchResult convertResultToSearchResult(top.cywin.onetv.movie.bean.Result result) {
        if (result == null)
            return null;

        List<MovieItem> movies = new ArrayList<>();
        if (result.getList() != null) {
            for (top.cywin.onetv.movie.bean.Vod vod : result.getList()) {
                MovieItem movie = convertVodToMovie(vod);
                if (movie != null) {
                    movies.add(movie);
                }
            }
        }

        return new SearchResult(
                movies,
                result.getPage(),
                result.getPagecount(),
                result.getTotal());
    }

    /**
     * 转换播放标志列表
     * 🔥 关键修复：处理null的urls，防止NullPointerException死循环
     */
    public static List<PlayFlag> convertVodFlags(top.cywin.onetv.movie.bean.Vod vod) {
        List<PlayFlag> flags = new ArrayList<>();

        if (vod != null && vod.getVodFlags() != null) {
            int i = 0;
            for (top.cywin.onetv.movie.bean.Flag flag : vod.getVodFlags()) {
                try {
                    // 🔥 关键修复：检查urls是否为null，如果为null则使用空字符串
                    String urls = flag.getUrls();
                    if (urls == null) {
                        Log.w(TAG, "⚠️ [播放线路修复] Flag的urls为null，使用空字符串: " + flag.getFlag());
                        urls = "";
                    }

                    flags.add(new PlayFlag(
                            flag.getFlag() != null ? flag.getFlag() : "",
                            urls,
                            i == 0 // 第一个为默认选中
                    ));
                    Log.d(TAG, "✅ [播放线路修复] 成功转换播放线路: " + flag.getFlag() + ", urls长度: " + urls.length());
                    i++;
                } catch (Exception e) {
                    Log.e(TAG, "❌ [播放线路修复] 转换播放线路失败: " + flag.getFlag(), e);
                    // 继续处理下一个，不中断整个流程
                }
            }
        }

        Log.d(TAG, "🎬 [播放线路修复] 总共转换播放线路数量: " + flags.size());
        return flags;
    }

    /**
     * 转换剧集列表
     */
    public static List<Episode> convertVodEpisodes(String flagUrls) {
        List<Episode> episodes = new ArrayList<>();

        if (flagUrls != null && !flagUrls.isEmpty()) {
            String[] episodeArray = flagUrls.split("#");

            for (int i = 0; i < episodeArray.length; i++) {
                String[] parts = episodeArray[i].split("\\$");
                if (parts.length >= 2) {
                    episodes.add(new Episode(
                            i,
                            parts[0], // 剧集名称
                            parts[1], // 播放地址
                            "", // playUrl - 初始为空，需要解析
                            false, // 未播放
                            0f // 进度
                    ));
                }
            }
        }

        return episodes;
    }

    /**
     * 转换观看历史
     */
    public static WatchHistory convertHistoryToWatchHistory(top.cywin.onetv.movie.bean.History history) {
        if (history == null)
            return null;

        return new WatchHistory(
                history.getVodId(),
                history.getVodName(),
                history.getVodPic() != null ? history.getVodPic() : "", // ✅ 添加vodPic参数
                history.getSiteKey(), // siteKey
                history.getVodRemarks() != null ? history.getVodRemarks() : "",
                history.getPosition(),
                history.getDuration(),
                history.getCreateTime(),
                history.getPosition() >= history.getDuration() * 0.9,
                history.getVodName(), // title
                history.getDuration() > 0 ? (float) history.getPosition() / history.getDuration() : 0f, // progress
                0 // episodeIndex
        );
    }

    /**
     * 转换收藏项
     */
    public static FavoriteItem convertKeepToFavorite(top.cywin.onetv.movie.bean.Keep keep) {
        if (keep == null)
            return null;

        return new FavoriteItem(
                keep.getVodId(),
                keep.getVodName(),
                keep.getVodPic(),
                keep.getSiteName(), // 使用siteName作为siteKey
                keep.getSiteName(),
                keep.getCreateTime());
    }

    /**
     * 批量转换Vod列表为MovieItem列表
     */
    public static List<MovieItem> convertVodListToMovieList(List<top.cywin.onetv.movie.bean.Vod> vodList) {
        List<MovieItem> movieList = new ArrayList<>();

        if (vodList != null) {
            for (top.cywin.onetv.movie.bean.Vod vod : vodList) {
                MovieItem movie = convertVodToMovie(vod);
                if (movie != null) {
                    movieList.add(movie);
                }
            }
        }

        return movieList;
    }

    /**
     * 批量转换Site列表为SiteInfo列表
     */
    public static List<SiteInfo> convertSiteListToSiteInfoList(List<top.cywin.onetv.movie.bean.Site> siteList) {
        List<SiteInfo> siteInfoList = new ArrayList<>();

        if (siteList != null) {
            for (top.cywin.onetv.movie.bean.Site site : siteList) {
                SiteInfo siteInfo = convertSiteToSiteInfo(site);
                if (siteInfo != null) {
                    siteInfoList.add(siteInfo);
                }
            }
        }

        return siteInfoList;
    }

    /**
     * 批量转换Class列表为CategoryInfo列表
     */
    public static List<CategoryInfo> convertClassListToCategoryList(List<top.cywin.onetv.movie.bean.Class> classList) {
        List<CategoryInfo> categoryList = new ArrayList<>();

        if (classList != null) {
            for (top.cywin.onetv.movie.bean.Class clazz : classList) {
                CategoryInfo category = convertClassToCategory(clazz);
                if (category != null) {
                    categoryList.add(category);
                }
            }
        }

        return categoryList;
    }

    /**
     * 转换云盘配置对象为UI模型
     */
    public static top.cywin.onetv.movie.ui.model.CloudDriveConfig convertToCloudDriveConfig(Object config) {
        if (config == null)
            return null;

        try {
            // 这里需要根据实际的FongMi_TV云盘配置对象进行转换
            // 暂时返回一个示例配置
            return new top.cywin.onetv.movie.ui.model.CloudDriveConfig(
                    "default_id",
                    "默认云盘",
                    "alist",
                    "http://localhost:5244",
                    "",
                    "",
                    true);
        } catch (Exception e) {
            Log.e(TAG, "转换云盘配置失败", e);
            return null;
        }
    }

    /**
     * 转换云盘文件对象为UI模型
     */
    public static top.cywin.onetv.movie.ui.model.CloudFile convertToCloudFile(Object file) {
        if (file == null)
            return null;

        try {
            // 这里需要根据实际的FongMi_TV云盘文件对象进行转换
            if (file instanceof top.cywin.onetv.movie.cloudrive.bean.CloudFile) {
                top.cywin.onetv.movie.cloudrive.bean.CloudFile cloudFile = (top.cywin.onetv.movie.cloudrive.bean.CloudFile) file;

                return new top.cywin.onetv.movie.ui.model.CloudFile(
                        cloudFile.getName(),
                        cloudFile.getPath(),
                        cloudFile.getSize(),
                        cloudFile.isFolder(),
                        System.currentTimeMillis(), // lastModified
                        cloudFile.getDownloadUrl(), // playUrl
                        cloudFile.isFolder() // isFolder (第7个参数)
                );
            }

            return null;
        } catch (Exception e) {
            Log.e(TAG, "转换云盘文件失败", e);
            return null;
        }
    }

    /**
     * 转换FongMi_TV的Flag为UI模型的PlayFlag
     */
    public static PlayFlag convertFlagToPlayFlag(top.cywin.onetv.movie.bean.Flag flag) {
        if (flag == null)
            return null;

        try {
            return new PlayFlag(
                    flag.getFlag(),
                    flag.getUrls(),
                    false // isSelected
            );
        } catch (Exception e) {
            Log.e(TAG, "转换播放线路失败", e);
            return null;
        }
    }

    /**
     * 转换UI模型的PlayFlag为FongMi_TV的Flag
     */
    public static top.cywin.onetv.movie.bean.Flag convertPlayFlagToFlag(PlayFlag playFlag) {
        if (playFlag == null)
            return null;

        try {
            top.cywin.onetv.movie.bean.Flag flag = new top.cywin.onetv.movie.bean.Flag();
            flag.setFlag(playFlag.getFlag());
            flag.setUrls(playFlag.getUrls());
            return flag;
        } catch (Exception e) {
            Log.e(TAG, "转换播放线路失败", e);
            return null;
        }
    }
}
