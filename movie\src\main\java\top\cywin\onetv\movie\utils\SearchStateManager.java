package top.cywin.onetv.movie.utils;

import android.util.Log;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.bean.Site;

/**
 * 🔥 原版FongMi_TV：搜索状态跟踪管理器
 * 实现站点搜索状态管理，支持剩余站点查询
 */
public class SearchStateManager {
    private static final String TAG = "SearchStateManager";
    private static SearchStateManager instance;

    // 🔥 搜索状态管理
    private String currentKeyword = "";
    private String currentSearchSessionId = "";
    private Set<String> searchedSiteKeys = new HashSet<>();
    private long searchStartTime = 0;
    private boolean isSearchActive = false;

    public static SearchStateManager getInstance() {
        if (instance == null) {
            synchronized (SearchStateManager.class) {
                if (instance == null) {
                    instance = new SearchStateManager();
                }
            }
        }
        return instance;
    }

    private SearchStateManager() {
        // 私有构造函数
    }

    /**
     * 开始新搜索
     */
    public void startNewSearch(String keyword) {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        // 🔥 修复：确保搜索状态完全重置
        synchronized (searchedSiteKeys) {
            this.currentKeyword = keyword;
            this.currentSearchSessionId = UUID.randomUUID().toString();
            this.searchedSiteKeys.clear(); // 清空已搜索站点集合
            this.searchStartTime = System.currentTimeMillis();
            this.isSearchActive = true;
        }

        // 🔥 修复：记录站点总数用于调试
        int totalSites = 0;
        try {
            totalSites = top.cywin.onetv.movie.api.config.VodConfig.get().getSites().size();
        } catch (Exception e) {
            Log.w("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [SEARCH_STATE_GET_TOTAL_SITES_ERROR] 获取站点总数失败: " + e.getMessage());
        }

        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_START_NEW",
                "开始新搜索: " + keyword + ", 会话ID: " + currentSearchSessionId + ", 站点总数: " + totalSites);
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_START_NEW] 开始新搜索: " + keyword + ", 会话ID: "
                + currentSearchSessionId + ", 站点总数: " + totalSites);
    }

    /**
     * 继续现有搜索
     */
    public boolean continueExistingSearch(String keyword) {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        if (keyword.equals(currentKeyword) && isSearchActive) {
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_CONTINUE",
                    "继续搜索: " + keyword + ", 会话ID: " + currentSearchSessionId + ", 已搜索站点数: " + searchedSiteKeys.size());
            Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_CONTINUE] 继续搜索: " + keyword + ", 已搜索站点数: "
                    + searchedSiteKeys.size());
            return true;
        } else {
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_CANNOT_CONTINUE",
                    "无法继续搜索: 关键词不匹配或搜索未激活");
            Log.w("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_CANNOT_CONTINUE] 无法继续搜索: 关键词不匹配或搜索未激活");
            return false;
        }
    }

    /**
     * 标记站点已搜索
     */
    public void markSiteSearched(String siteKey) {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        synchronized (searchedSiteKeys) {
            // 🔥 修复：确保站点key被正确添加到集合中
            boolean wasAdded = searchedSiteKeys.add(siteKey);
            if (!wasAdded) {
                Log.w("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_DUPLICATE_SITE] 重复标记站点: " + siteKey);
            }
        }

        int currentCount = searchedSiteKeys.size();
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_MARK_SITE",
                "站点搜索完成: " + siteKey + ", 已搜索: " + currentCount);
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_MARK_SITE] 站点搜索完成: " + siteKey + ", 已搜索: "
                + currentCount);
    }

    /**
     * 检查站点是否已搜索
     */
    public boolean isSiteSearched(String siteKey) {
        synchronized (searchedSiteKeys) {
            return searchedSiteKeys.contains(siteKey);
        }
    }

    /**
     * 获取剩余未搜索的站点
     */
    public List<Site> getRemainingUnSearchedSites() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        try {
            List<Site> allSites = VodConfig.get().getSites();
            List<Site> remainingSites = allSites.stream()
                    .filter(site -> !isSiteSearched(site.getKey()))
                    .collect(Collectors.toList());

            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_GET_REMAINING",
                    "获取剩余站点: 总站点数=" + allSites.size() + ", 剩余=" + remainingSites.size());
            Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_GET_REMAINING] 获取剩余站点: 总站点数="
                    + allSites.size() + ", 剩余=" + remainingSites.size());

            return remainingSites;
        } catch (Exception e) {
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_GET_REMAINING_ERROR",
                    "获取剩余站点失败: " + e.getMessage());
            Log.e("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_GET_REMAINING_ERROR] 获取剩余站点失败", e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取当前搜索关键词
     */
    public String getCurrentKeyword() {
        return currentKeyword;
    }

    /**
     * 获取当前搜索会话ID
     */
    public String getCurrentSearchSessionId() {
        return currentSearchSessionId;
    }

    /**
     * 获取已搜索站点数量
     */
    public int getSearchedSiteCount() {
        synchronized (searchedSiteKeys) {
            return searchedSiteKeys.size();
        }
    }

    /**
     * 获取搜索开始时间
     */
    public long getSearchStartTime() {
        return searchStartTime;
    }

    /**
     * 检查搜索是否激活
     */
    public boolean isSearchActive() {
        return isSearchActive;
    }

    /**
     * 停止搜索
     */
    public void stopSearch() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        this.isSearchActive = false;

        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_STOP",
                "停止搜索: " + currentKeyword + ", 会话ID: " + currentSearchSessionId);
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_STOP] 停止搜索: " + currentKeyword + ", 会话ID: "
                + currentSearchSessionId);
    }

    /**
     * 重置搜索状态
     */
    public void resetSearchState() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SEARCH_STATE_RESET",
                "重置搜索状态: 清理关键词=" + currentKeyword + ", 会话ID=" + currentSearchSessionId);
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SEARCH_STATE_RESET] 重置搜索状态");

        this.currentKeyword = "";
        this.currentSearchSessionId = "";
        this.searchedSiteKeys.clear();
        this.searchStartTime = 0;
        this.isSearchActive = false;
    }

    /**
     * 获取搜索统计信息
     */
    public String getSearchStats() {
        long duration = System.currentTimeMillis() - searchStartTime;
        return String.format("关键词: %s, 会话ID: %s, 已搜索: %d, 耗时: %dms, 激活: %s",
                currentKeyword, currentSearchSessionId, searchedSiteKeys.size(), duration, isSearchActive);
    }

    /**
     * 检查是否为相同搜索
     */
    public boolean isSameSearch(String keyword) {
        return keyword != null && keyword.equals(currentKeyword) && isSearchActive;
    }

    /**
     * 获取搜索进度百分比
     */
    public int getSearchProgress() {
        try {
            int totalSites = VodConfig.get().getSites().size();
            if (totalSites == 0)
                return 100;

            int searchedCount = getSearchedSiteCount();
            return Math.min(100, (searchedCount * 100) / totalSites);
        } catch (Exception e) {
            Log.e(TAG, "获取搜索进度失败", e);
            return 0;
        }
    }
}
