package top.cywin.onetv.movie.utils

import android.util.Log
import androidx.navigation.NavController
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.model.SiteViewModel
import top.cywin.onetv.movie.navigation.MovieRoutes

/**
 * 电影点击处理工具类 - 实现完全直通设计
 * 
 * 功能：
 * 1. 统一处理所有UI层的电影点击逻辑
 * 2. 直接调用SiteViewModel，不通过RepositoryAdapter中转
 * 3. 实现与原版FongMi_TV完全一致的架构
 * 4. 使用VOD_FLOW日志系统进行全流程跟踪
 * 
 * 架构：UI层 → MovieClickHandler → SiteViewModel → Spider
 */
object MovieClickHandler {
    
    private const val TAG = "MovieClickHandler"
    
    /**
     * 处理电影点击事件 - 完全直通设计
     * 
     * @param vodId 电影ID
     * @param movieName 电影名称
     * @param siteKey 站点Key
     * @param vodPic 电影海报URL（可选）
     * @param navController 导航控制器
     * @param siteViewModel SiteViewModel实例
     * @param flowId 流程ID（可选，用于日志跟踪）
     */
    fun handleMovieClick(
        vodId: String?,
        movieName: String,
        siteKey: String?,
        vodPic: String? = null,
        navController: NavController,
        siteViewModel: SiteViewModel,
        flowId: String? = null
    ) {
        val currentFlowId = flowId ?: VodFlowTracker.generateFlowId()
        
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [MOVIE_CLICK_HANDLER] 开始处理电影点击")
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [MOVIE_CLICK_INFO] vodId=$vodId, movieName=$movieName, siteKey=$siteKey")
        VodFlowTracker.logFlowStep(currentFlowId, "MOVIE_CLICK_HANDLER", "处理电影点击: $movieName")
        
        try {
            // 🔥 关键：直接判断ID类型，不通过适配器中转
            if (vodId.isNullOrEmpty() || vodId.startsWith("msearch:")) {
                // 搜索模式：直接调用SiteViewModel搜索
                handleSearchMode(movieName, navController, siteViewModel, currentFlowId)
            } else {
                // 详情模式：直接调用SiteViewModel获取详情
                handleDetailMode(vodId, movieName, siteKey, vodPic, navController, siteViewModel, currentFlowId)
            }
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [MOVIE_CLICK_ERROR] 电影点击处理失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "MOVIE_CLICK_ERROR", "处理失败: ${e.message}")
        }
    }
    
    /**
     * 处理搜索模式 - 直接调用SiteViewModel搜索
     */
    private fun handleSearchMode(
        movieName: String,
        navController: NavController,
        siteViewModel: SiteViewModel,
        flowId: String
    ) {
        Log.d("VOD_FLOW", "[FlowID:$flowId] [SEARCH_MODE] 进入搜索模式: $movieName")
        VodFlowTracker.logFlowStep(flowId, "SEARCH_MODE", "搜索模式: $movieName")
        
        if (movieName.isEmpty()) {
            Log.w("VOD_FLOW", "[FlowID:$flowId] [SEARCH_MODE_ERROR] 电影名称为空，无法搜索")
            return
        }
        
        try {
            // 🔥 完全直通设计：直接调用SiteViewModel搜索
            Log.d("VOD_FLOW", "[FlowID:$flowId] [DIRECT_SEARCH_CALL] 直接调用SiteViewModel.searchContent")
            siteViewModel.searchContent(movieName, false)
            
            // 🔥 直接导航到搜索结果列表
            Log.d("VOD_FLOW", "[FlowID:$flowId] [DIRECT_NAVIGATION] 直接导航到搜索结果列表")
            navController.navigate(MovieRoutes.searchResultList(movieName))
            
            Log.d("VOD_FLOW", "[FlowID:$flowId] [SEARCH_MODE_SUCCESS] 搜索模式处理成功")
            VodFlowTracker.logFlowStep(flowId, "SEARCH_MODE_SUCCESS", "搜索请求已发送")
            
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$flowId] [SEARCH_MODE_ERROR] 搜索模式处理失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(flowId, "SEARCH_MODE_ERROR", "搜索失败: ${e.message}")
        }
    }
    
    /**
     * 处理详情模式 - 直接调用SiteViewModel获取详情
     */
    private fun handleDetailMode(
        vodId: String,
        movieName: String,
        siteKey: String?,
        vodPic: String?,
        navController: NavController,
        siteViewModel: SiteViewModel,
        flowId: String
    ) {
        Log.d("VOD_FLOW", "[FlowID:$flowId] [DETAIL_MODE] 进入详情模式: $movieName")
        VodFlowTracker.logFlowStep(flowId, "DETAIL_MODE", "详情模式: $movieName")
        
        if (siteKey.isNullOrEmpty()) {
            Log.w("VOD_FLOW", "[FlowID:$flowId] [DETAIL_MODE_ERROR] 站点Key为空，无法获取详情")
            return
        }
        
        try {
            // 🔥 完全直通设计：直接导航到详情页面
            // 详情页面内部会调用SiteViewModel.detailContent获取数据
            Log.d("VOD_FLOW", "[FlowID:$flowId] [DIRECT_DETAIL_NAVIGATION] 直接导航到详情页面")
            val route = MovieRoutes.detail(vodId, siteKey, vodPic, flowId)
            navController.navigate(route)
            
            Log.d("VOD_FLOW", "[FlowID:$flowId] [DETAIL_MODE_SUCCESS] 详情模式处理成功")
            VodFlowTracker.logFlowStep(flowId, "DETAIL_MODE_SUCCESS", "已导航到详情页面")
            
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$flowId] [DETAIL_MODE_ERROR] 详情模式处理失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(flowId, "DETAIL_MODE_ERROR", "详情处理失败: ${e.message}")
        }
    }
    
    /**
     * 处理Vod对象的电影点击 - 便捷方法
     */
    fun handleVodClick(
        vod: Vod,
        navController: NavController,
        siteViewModel: SiteViewModel,
        flowId: String? = null
    ) {
        handleMovieClick(
            vodId = vod.vodId,
            movieName = vod.vodName ?: "",
            siteKey = vod.siteKey,
            vodPic = vod.vodPic,
            navController = navController,
            siteViewModel = siteViewModel,
            flowId = flowId
        )
    }
    
    /**
     * 处理MovieItem的电影点击 - 便捷方法
     */
    fun handleMovieItemClick(
        vodId: String?,
        movieName: String,
        siteKey: String?,
        vodPic: String? = null,
        navController: NavController,
        siteViewModel: SiteViewModel,
        flowId: String? = null
    ) {
        handleMovieClick(
            vodId = vodId,
            movieName = movieName,
            siteKey = siteKey,
            vodPic = vodPic,
            navController = navController,
            siteViewModel = siteViewModel,
            flowId = flowId
        )
    }
}
