package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.delay
import kotlin.coroutines.coroutineContext
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.bean.Site
import top.cywin.onetv.movie.bean.Class
import top.cywin.onetv.movie.bean.Vod
import android.util.Log

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.*
import top.cywin.onetv.movie.event.MovieIdTransformEvent
import top.cywin.onetv.movie.ui.model.*
import top.cywin.onetv.movie.adapter.ViewModelAdapter
// 🔥 添加缺失的导入
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * 首页UI状态数据类 - 完整版本
 */
data class MovieUiState(
    // 基础状态
    val isLoading: Boolean = false,
    val error: String? = null,
    val loadingMessage: String = "",
    val loadingProgress: Float = 0f,

    // 加载阶段状态
    val isLoadingConfig: Boolean = false,
    val isLoadingRecommend: Boolean = false,
    val configLoadProgress: Float = 0f,
    val recommendLoadProgress: Float = 0f,

    // 配置相关
    val isStoreHouseIndex: Boolean = false,
    val storeHouseName: String = "",
    val availableRoutes: List<VodConfigUrl> = emptyList(),
    val selectedRoute: VodConfigUrl? = null,
    val showRouteSelector: Boolean = false,
    val showConfigSetup: Boolean = false,
    val showWelcomeScreen: Boolean = false,

    // 站点相关
    val currentSite: top.cywin.onetv.movie.bean.Site? = null,
    val availableSites: List<top.cywin.onetv.movie.bean.Site> = emptyList(),
    val showSiteSelector: Boolean = false,
    val siteList: List<SiteInfo> = emptyList(),

    // 内容相关
    val categories: List<CategoryInfo> = emptyList(),
    val selectedCategory: CategoryInfo? = null,
    val recommendMovies: List<MovieItem> = emptyList(),
    val homeCategories: List<HomeCategorySection> = emptyList(),
    val hotMovies: List<MovieItem> = emptyList(),
    val newMovies: List<MovieItem> = emptyList(),
    val currentCategoryName: String? = null,  // 当前显示的分类名称
    val categoryMovies: List<MovieItem> = emptyList(),  // 分类电影列表

    // UI控制
    val showSearch: Boolean = false,
    val showSettings: Boolean = false,
    val refreshing: Boolean = false,

    // 网络状态
    val networkState: NetworkState = NetworkState(),

    // 历史记录相关
    val watchHistories: List<WatchHistory> = emptyList(),
    val isLoadingHistory: Boolean = false,

    // 收藏相关
    val favoritesList: List<top.cywin.onetv.movie.bean.Keep> = emptyList(),
    val isLoadingFavorites: Boolean = false,

    // 其他状态
    val lastUpdateTime: Long = 0
)

/**
 * OneTV Movie首页ViewModel - 完整版本
 * 通过适配器系统调用FongMi_TV解析功能，完整的事件驱动架构
 */
class MovieViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_VM"
    }

    // ✅ 通过MovieApp访问适配器系统 - 安全的延迟初始化
    private val movieApp: MovieApp by lazy {
        Log.d(TAG, "🔄 [第5阶段] 开始获取MovieApp实例")
        try {
            // ✅ 检查FongMi_TV App类是否已初始化
            val fongmiApp = top.cywin.onetv.movie.App.get()
            if (fongmiApp == null) {
                Log.w(TAG, "⚠️ [第5阶段] FongMi_TV App类未初始化，创建最小化实例")
                // 不等待，直接创建最小化实例，避免阻塞UI
                val tempApp = MovieApp()
                Log.d(TAG, "✅ [第5阶段] 创建最小化MovieApp实例")
                tempApp
            } else {
                val app = MovieApp.getInstance()
                Log.d(TAG, "✅ [第5阶段] MovieApp实例获取成功")
                app
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] MovieApp实例获取失败，创建临时实例", e)
            // 创建临时实例，避免完全崩溃
            MovieApp()
        }
    }
    private val repositoryAdapter by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取RepositoryAdapter")
        movieApp.repositoryAdapter
    }
    private val viewModelAdapter by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取ViewModelAdapter")
        movieApp.viewModelAdapter
    }
    private val vodConfig by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取VodConfig")
        try {
            movieApp.vodConfig
        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] 获取VodConfig失败，使用默认实例", e)
            // 创建一个默认的VodConfig实例，避免崩溃
            top.cywin.onetv.movie.api.config.VodConfig()
        }
    }

    private val _uiState = MutableStateFlow(MovieUiState())
    val uiState: StateFlow<MovieUiState> = _uiState.asStateFlow()

    // 导航事件
    private val _navigationEvent = MutableStateFlow<NavigationEvent?>(null)
    val navigationEvent: StateFlow<NavigationEvent?> = _navigationEvent.asStateFlow()

    /**
     * 🔥 原版FongMi_TV直接导航支持：设置NavController
     */
    fun setNavController(navController: androidx.navigation.NavController) {
        repositoryAdapter.setNavController(navController)
        Log.d(TAG, "🔥 [原版直接导航] NavController已传递给RepositoryAdapter")
    }

    // 清理导航事件
    fun clearNavigationEvent() {
        Log.d(TAG, "🎯 [电影ID跟踪] 清理导航事件")
        _navigationEvent.value = null
    }

    // ✅ 防止EventBus事件循环的标志
    private var isLoadingHomeData = false
    private var isHandlingConfigUpdate = false

    // 🔥 原版FongMi_TV线路切换状态管理
    @Volatile
    private var isRouteSwitching = false
    private val routeSwitchMutex = Mutex()

    init {
        Log.d(TAG, "🎬 [第5阶段] MovieViewModel开始初始化")
        Log.d(TAG, "📍 位置: MovieViewModel.kt:114")
        Log.d(TAG, "⏰ 时间戳: ${System.currentTimeMillis()}")

        try {
            // ✅ 强制触发lazy初始化，逐步检查每个组件
            Log.d(TAG, "🔄 [第5阶段] 开始初始化MovieApp")
            val app = movieApp // 触发movieApp的lazy初始化
            Log.d(TAG, "✅ [第5阶段] MovieApp初始化完成: ${app.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化RepositoryAdapter")
            val repo = repositoryAdapter // 触发repositoryAdapter的lazy初始化
            Log.d(TAG, "✅ [第5阶段] RepositoryAdapter初始化完成: ${repo.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化ViewModelAdapter")
            val viewAdapter = viewModelAdapter // 触发viewModelAdapter的lazy初始化
            Log.d(TAG, "✅ [第5阶段] ViewModelAdapter初始化完成: ${viewAdapter.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化VodConfig")
            val config = vodConfig // 触发vodConfig的lazy初始化
            Log.d(TAG, "✅ [第5阶段] VodConfig初始化完成: ${config.javaClass.simpleName}")

            // ✅ 注册EventBus监听FongMi_TV事件
            Log.d(TAG, "🔗 [第5阶段] 注册EventBus事件监听")
            EventBus.getDefault().register(this)
            Log.d(TAG, "✅ [第5阶段] EventBus注册成功")

            // 🔥 修复：注册ConfigUpdateEvent监听，用于线路切换完成通知
            Log.d(TAG, "🔗 [第5阶段] 准备监听ConfigUpdateEvent事件")

            // ✅ 不在init中自动加载首页数据，等待UI准备好后手动调用
            Log.d(TAG, "🚀 [第5阶段] ViewModel初始化完成，等待UI调用loadHomeData")
            // loadHomeData() // 移除自动调用

        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] MovieViewModel初始化失败", e)
            Log.e(TAG, "❌ [第5阶段] 异常详情: ${e.javaClass.simpleName}: ${e.message}")
            e.printStackTrace()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "初始化失败: ${e.message}"
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MovieViewModel 清理")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听电影ID转换事件 - 处理导航逻辑
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMovieIdTransform(event: MovieIdTransformEvent) {
        Log.d(TAG, "📡 [FongMi_TV兼容] 收到ID转换事件: success=${event.isSuccess}")

        if (event.isSuccess && event.realVodId != null) {
            Log.d(TAG, "✅ [FongMi_TV兼容] ID转换成功，开始导航: ${event.movieName} -> ${event.realVodId}")

            // 发送导航事件到UI层
            _navigationEvent.value = top.cywin.onetv.movie.event.NavigationEvent.NavigateToDetail(
                event.realVodId,
                event.siteKey,
                event.movieName
            )
        } else {
            Log.e(TAG, "❌ [FongMi_TV兼容] ID转换失败: ${event.error}")
            // 可以在这里显示错误提示
        }
    }

    /**
     * 监听搜索结果列表导航事件 - 处理推荐电影点击后的导航
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNavigateToSearchResultList(event: top.cywin.onetv.movie.event.NavigationEvent.NavigateToSearchResultList) {
        Log.d(TAG, "📡 [修改逻辑] 收到搜索结果列表导航事件: keyword=${event.keyword}, count=${event.results.size}")

        // 转发导航事件到UI层
        _navigationEvent.value = event

        Log.d(TAG, "✅ [修改逻辑] 搜索结果列表导航事件已转发到UI层")
    }

    /**
     * 监听配置更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onConfigUpdate(event: ConfigUpdateEvent) {
        Log.d(TAG, "📡 收到配置更新事件: success=${event.isSuccess}")

        // ✅ 防止循环处理
        if (isHandlingConfigUpdate) {
            Log.w(TAG, "⚠️ 正在处理配置更新，跳过重复事件")
            return
        }

        // ✅ 允许成功的配置更新事件和WELCOME_SCREEN事件在加载过程中正常处理
        if (isLoadingHomeData && !event.isSuccess && event.errorMessage != "WELCOME_SCREEN") {
            Log.w(TAG, "⚠️ 正在加载首页数据，跳过非成功且非欢迎界面的配置更新事件")
            return
        }

        if (event.isSuccess && event.config != null) {
            Log.d("VOD_FLOW", "[CONFIG_SUCCESS] 配置更新成功，处理配置")
            isHandlingConfigUpdate = true
            try {
                // ✅ 确保在主线程更新UI状态
                viewModelScope.launch(Dispatchers.Main.immediate) {
                    // ✅ 先重置加载标志
                    isLoadingHomeData = false

                    // ✅ 配置加载成功，开始加载推荐内容（仍在欢迎页面）
                    Log.d("VOD_FLOW", "[CONFIG_SUCCESS_UI] 配置加载成功，开始加载推荐内容")
                    _uiState.value = _uiState.value.copy(
                        showWelcomeScreen = true,  // 继续显示欢迎页面
                        isLoading = true,          // 继续加载状态
                        isLoadingConfig = false,   // 配置加载完成
                        isLoadingRecommend = true, // 开始推荐内容加载
                        configLoadProgress = 1f,   // 配置加载100%
                        recommendLoadProgress = 0f, // 推荐内容加载0%
                        error = null,
                        loadingMessage = "正在加载推荐内容..."
                    )
                }

                handleConfigUpdateSuccess(event.config)
            } finally {
                isHandlingConfigUpdate = false
            }
        } else {
            Log.d("VOD_FLOW", "[CONFIG_FAILED] 配置更新失败: ${event.errorMessage}")

            // ✅ 重构状态管理：所有配置相关状态都在欢迎页面中处理
            viewModelScope.launch(Dispatchers.Main.immediate) {
                try {
                    isLoadingHomeData = false

                    if (event.errorMessage == "WELCOME_SCREEN") {
                        // 初始状态：显示欢迎界面
                        Log.d("VOD_FLOW", "[WELCOME_INITIAL] 显示初始欢迎界面")
                        _uiState.value = _uiState.value.copy(
                            showWelcomeScreen = true,
                            isLoading = false,
                            error = null,
                            loadingMessage = ""
                        )
                    } else {
                        // 配置加载失败：在欢迎界面中显示错误状态
                        Log.d("VOD_FLOW", "[CONFIG_ERROR] 配置加载失败，在欢迎界面显示错误")
                        _uiState.value = _uiState.value.copy(
                            showWelcomeScreen = true,
                            isLoading = false,
                            error = event.errorMessage ?: "配置加载失败",
                            loadingMessage = ""
                        )
                    }
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[CONFIG_UPDATE_ERROR] 配置更新处理异常", e)
                    // 异常情况下设置欢迎界面状态
                    _uiState.value = _uiState.value.copy(
                        showWelcomeScreen = true,
                        isLoading = false,
                        error = "配置更新异常: ${e.message}",
                        loadingMessage = ""
                    )
                }
            }
        }
    }

    /**
     * 监听首页内容事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onHomeContent(event: HomeContentEvent) {
        Log.d(TAG, "📡 收到首页内容事件: success=${event.isSuccess}")
        Log.d(TAG, "📊 分类数量: ${event.categories.size}")
        Log.d(TAG, "📊 推荐内容数量: ${event.recommendVods.size}")

        if (event.isSuccess) {
            handleHomeContentSuccess(event.categories, event.recommendVods)
        } else {
            Log.e(TAG, "❌ 首页内容加载失败")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "首页内容加载失败"
            )
        }
    }

    /**
     * 监听分类内容事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCategoryContent(event: CategoryContentEvent) {
        Log.d(TAG, "📡 收到分类内容: typeId=${event.typeId}, count=${event.vods.size}")

        handleCategoryContentUpdate(event)
    }

    /**
     * 监听搜索结果事件（用于推荐内容）
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchResult(event: SearchResultEvent) {
        Log.d(TAG, "📡 收到搜索结果: keyword=${event.keyword}, count=${event.results.size}")

        // 如果是首页推荐搜索（空关键词或特定关键词）
        if (event.keyword.isEmpty() || event.keyword == "推荐") {
            val movieItems = event.results.map { vod ->
                ViewModelAdapter.convertVodToMovie(vod)
            }.filterNotNull()

            _uiState.value = _uiState.value.copy(
                recommendMovies = movieItems
            )
        }
    }

    /**
     * 监听站点变更事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSiteChange(event: SiteChangeEvent) {
        Log.d(TAG, "📡 收到站点变更事件: success=${event.isSuccess}")

        if (event.isSuccess && event.site != null) {
            // 🔥 修复：站点切换成功后，更新UI状态并开始加载推荐内容
            _uiState.value = _uiState.value.copy(
                currentSite = event.site,
                isLoadingConfig = false,    // 站点切换完成
                isLoadingRecommend = true,  // 开始加载推荐内容
                configLoadProgress = 1f,    // 配置加载100%
                recommendLoadProgress = 0f, // 推荐内容加载0%
                loadingMessage = "正在加载推荐内容..."
            )

            // ✅ 重新启用站点变更后的内容加载，添加防循环机制
            if (!isLoadingHomeData && !isHandlingConfigUpdate) {
                Log.d(TAG, "🔄 站点变更后重新加载首页内容")
                viewModelScope.launch {
                    try {
                        // ✅ 直接加载站点内容，不触发配置重新加载
                        repositoryAdapter.getCategories()
                        repositoryAdapter.getRecommendContent()
                        Log.d(TAG, "✅ 站点变更后内容加载请求已发送")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 站点变更后内容加载失败", e)
                        // 🔥 修复：加载失败时隐藏欢迎页面并显示错误
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoadingConfig = false,
                            isLoadingRecommend = false,
                            showWelcomeScreen = false,
                            error = "站点内容加载失败: ${e.message}"
                        )
                    }
                }
            } else {
                Log.w(TAG, "⚠️ 正在加载数据或处理配置更新，跳过站点变更重载")
            }
        }
    }

    // ✅ 防止错误事件循环的标志
    private var isHandlingError = false
    private var lastErrorTime = 0L
    private var lastErrorMessage = ""

    // 🔥 删除：ContentDetailEvent处理已移至RepositoryAdapter
    // 现在按照项目架构：FongMi_TV → EventBus → RepositoryAdapter → NavigationEvent → ViewModel

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        val errorMsg = event.msg ?: "未知错误"
        val currentTime = System.currentTimeMillis()

        // ✅ 防止错误事件循环
        if (isHandlingError) {
            Log.w(TAG, "⚠️ 正在处理错误事件，跳过重复事件")
            return
        }

        // ✅ 防止相同错误在短时间内重复处理
        if (errorMsg == lastErrorMessage && (currentTime - lastErrorTime) < 1000) {
            Log.w(TAG, "⚠️ 相同错误在1秒内重复，跳过处理: $errorMsg")
            return
        }

        // ✅ 过滤空错误消息
        if (errorMsg.isBlank() || errorMsg == "null") {
            Log.w(TAG, "⚠️ 跳过空错误消息")
            return
        }

        isHandlingError = true
        lastErrorTime = currentTime
        lastErrorMessage = errorMsg

        try {
            Log.e(TAG, "📡 收到错误事件: $errorMsg")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = errorMsg
            )
        } finally {
            isHandlingError = false
        }
    }

    // ===== 公共方法 =====

    /**
     * 加载首页数据 - 通过适配器调用FongMi_TV解析系统
     */
    fun loadHomeData() {
        Log.d(TAG, "🎬 [第6阶段] loadHomeData方法被调用")
        Log.d(TAG, "📍 位置: MovieViewModel.kt:235")
        Log.d(TAG, "⏰ 时间戳: ${System.currentTimeMillis()}")

        // ✅ 防止重复加载
        if (isLoadingHomeData) {
            Log.w(TAG, "⚠️ 正在加载首页数据，跳过重复调用")
            return
        }

        // ✅ 修复：如果正在显示站点选择器，不触发配置加载
        val currentState = _uiState.value
        if (currentState.showSiteSelector) {
            Log.w(TAG, "⚠️ 正在显示站点选择器，跳过首页数据加载")
            return
        }

        // 🔥 修复：线路切换后需要强制重新加载，不使用缓存
        // 只有在非欢迎页面状态下才检查缓存
        if (!currentState.showWelcomeScreen && !currentState.categories.isEmpty() && !currentState.homeCategories.isEmpty()) {
            Log.d(TAG, "✅ [第6阶段] 首页数据已缓存，分类数: ${currentState.categories.size}，跳过重复加载")
            return
        }

        isLoadingHomeData = true
        viewModelScope.launch {
            try {
                Log.d("VOD_FLOW", "[LOAD_HOME_START] 开始加载首页数据")

                // ✅ 重构状态管理：配置加载过程始终在欢迎页面中进行
                Log.d("VOD_FLOW", "[LOAD_HOME_WELCOME] 设置欢迎页面加载状态")
                _uiState.value = _uiState.value.copy(
                    showWelcomeScreen = true,  // 始终显示欢迎页面
                    isLoading = true,          // 在欢迎页面中显示加载状态
                    isLoadingConfig = true,
                    isLoadingRecommend = false,
                    configLoadProgress = 0f,
                    recommendLoadProgress = 0f,
                    error = null,
                    loadingMessage = "正在加载配置文件...速度与当前网络环境有关"
                )

                // ✅ 通过适配器加载配置，添加详细的异常处理和进度跟踪
                Log.d("VOD_FLOW", "[LOAD_CONFIG_START] 开始加载配置文件")

                // ✅ 启动配置加载进度模拟
                val configProgressJob = launch {
                    var progress = 0f
                    while (progress < 1f && _uiState.value.isLoadingConfig) {
                        delay(100)
                        progress += 0.02f
                        if (progress > 1f) progress = 1f

                        _uiState.value = _uiState.value.copy(
                            configLoadProgress = progress
                        )
                    }
                }

                try {
                    Log.d("VOD_FLOW", "[LOAD_CONFIG_ADAPTER] RepositoryAdapter实例: ${repositoryAdapter.javaClass.simpleName}")
                    repositoryAdapter.loadConfig()
                    Log.d("VOD_FLOW", "[LOAD_CONFIG_SUCCESS] repositoryAdapter.loadConfig()调用成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[LOAD_CONFIG_ERROR] repositoryAdapter.loadConfig()调用失败", e)
                    configProgressJob.cancel()
                    // ✅ 如果调用失败，在欢迎页面显示错误
                    _uiState.value = _uiState.value.copy(
                        showWelcomeScreen = true,
                        isLoading = false,
                        isLoadingConfig = false,
                        isLoadingRecommend = false,
                        error = "配置加载失败: ${e.message}",
                        loadingMessage = ""
                    )
                    return@launch
                }

                // ✅ 配置加载是异步的，通过EventBus接收结果
                Log.d("VOD_FLOW", "[LOAD_CONFIG_WAIT] 配置加载请求已发送，等待EventBus事件")

                // ✅ 配置加载完成后的处理将在ConfigUpdateEvent中进行
                Log.d("VOD_FLOW", "[LOAD_HOME_COMPLETE] 配置加载流程启动完成")

            } catch (e: Exception) {
                Log.e(TAG, "💥 首页数据加载失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "首页数据加载失败: ${e.message}"
                )
            } finally {
                // ✅ 配置加载是异步的，不在这里重置标志
                // 标志将在配置更新事件处理完成后重置
                Log.d(TAG, "🔄 [第6阶段] 首页数据加载请求完成，等待配置更新事件")
            }
        }
    }

    /**
     * 检查是否为仓库索引文件
     */
    private fun checkIfStoreHouseIndex(config: top.cywin.onetv.movie.api.config.VodConfig): Boolean {
        return config.sites.any { site ->
            site.name?.contains("仓库") == true ||
            site.api?.contains("index") == true
        }
    }

    /**
     * 处理仓库索引文件
     */
    private suspend fun handleStoreHouseIndex(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d("ONETV_MOVIE", "🏪 检测到仓库索引文件")

        // 获取配置URL列表
        val configUrls = config.sites.map { site ->
            VodConfigUrl(
                name = site.name ?: "未知线路",
                url = site.api ?: ""
            )
        }

        Log.d("ONETV_MOVIE", "📋 可用线路数: ${configUrls.size}")

        // 设置仓库索引状态
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            isStoreHouseIndex = true,
            storeHouseName = "默认仓库",
            availableRoutes = configUrls,
            showRouteSelector = false,
            error = null
        )

        // 自动选择第一条线路
        if (configUrls.isNotEmpty()) {
            selectRoute(configUrls[0])
        }
    }

    /**
     * 加载正常配置
     */
    private suspend fun loadNormalConfig(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d(TAG, "📋 加载正常配置，站点数: ${config.sites.size}")

        // 🔥 修复：确保使用新配置的默认站点，而不是旧的home站点
        var currentSite = config.home

        // 🔥 修复：如果当前站点为空或者不在新配置的站点列表中，使用第一个站点作为默认站点
        if (currentSite == null || !config.sites.contains(currentSite)) {
            if (config.sites.isNotEmpty()) {
                currentSite = config.sites[0]
                // 🔥 修复：强制设置新的默认站点到VodConfig
                config.setHome(currentSite)
                Log.d(TAG, "🔥 [loadNormalConfig] 强制设置新的默认站点: ${currentSite?.name}")
                Log.d(TAG, "🔥 [loadNormalConfig] 站点总数: ${config.sites.size}")
            }
        } else {
            // 🔥 修复：即使当前站点存在，也要确保它是新配置中的第一个站点（线路切换场景）
            if (config.sites.isNotEmpty() && currentSite != config.sites[0]) {
                Log.d(TAG, "🔥 [loadNormalConfig] 检测到线路切换，更新为新线路的第一个站点")
                currentSite = config.sites[0]
                config.setHome(currentSite)
                Log.d(TAG, "🔥 [loadNormalConfig] 线路切换后设置默认站点: ${currentSite?.name}")
            }
        }

        Log.d(TAG, "🏠 [loadNormalConfig] 最终当前站点: ${currentSite?.name}")

        // ✅ 转换站点列表为UI数据
        val siteInfos = config.sites.map { site ->
            ViewModelAdapter.convertSiteToSiteInfo(site)
        }.filterNotNull()

        // 🔥 修复：不要立即设置isLoading=false，保持欢迎页面直到内容加载完成
        _uiState.value = _uiState.value.copy(
            currentSite = currentSite,
            siteList = siteInfos,
            error = null,
            loadingMessage = "正在加载推荐内容...",
            showRouteSelector = false // 🔥 修复：线路切换后关闭线路选择器
            // 🔥 修复：保持isLoading=true和showWelcomeScreen=true，直到内容加载完成
        )

        Log.d(TAG, "✅ 正常配置加载完成，UI状态已更新")
        Log.d(TAG, "📊 当前UI状态: isLoading=${_uiState.value.isLoading}, error=${_uiState.value.error}")
        Log.d(TAG, "📊 站点数量: ${siteInfos.size}, 当前站点: ${currentSite?.name}")

        // ✅ 配置加载完成后，立即加载内容
        Log.d(TAG, "🔄 配置加载完成，开始加载首页内容（包含分类和推荐）")

        // ✅ 启动推荐内容加载进度模拟
        val recommendProgressJob = viewModelScope.launch {
            var progress = 0f
            while (progress < 1f && _uiState.value.isLoadingRecommend) {
                delay(150)
                progress += 0.03f
                if (progress > 1f) progress = 1f

                _uiState.value = _uiState.value.copy(
                    recommendLoadProgress = progress
                )
            }
        }

        try {
            // ✅ 修复：只调用一次homeContent，它会同时返回分类和推荐内容
            repositoryAdapter.getCategories()
            Log.d(TAG, "✅ 首页内容加载请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 内容加载请求失败", e)
            recommendProgressJob.cancel()
        }
    }

    /**
     * 加载分类内容 - 在主页显示分类内容
     */
    fun loadCategoryContent(typeId: String, typeName: String) {
        Log.d(TAG, "📂 [修复逻辑] 加载分类内容: typeId=$typeId, typeName=$typeName")

        // 🔥 修复逻辑：不显示加载状态，直接设置分类名称和清空电影列表
        _uiState.value = _uiState.value.copy(
            isLoading = false,  // 不显示加载状态
            error = null,
            currentCategoryName = typeName,
            categoryMovies = emptyList()  // 清空之前的分类电影
        )

        try {
            // 通过RepositoryAdapter加载分类内容
            repositoryAdapter.getContentList(typeId, 1, emptyMap())
            Log.d(TAG, "✅ [修复逻辑] 分类内容加载请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "❌ [修复逻辑] 分类内容加载失败", e)
            _uiState.value = _uiState.value.copy(
                error = "分类内容加载失败: ${e.message}"
            )
        }
    }

    /**
     * 清除分类内容，返回主页
     */
    fun clearCategoryContent() {
        Log.d(TAG, "🏠 [修复逻辑] 清除分类内容，返回主页")
        _uiState.value = _uiState.value.copy(
            currentCategoryName = null,
            categoryMovies = emptyList()
        )
    }

    /**
     * 处理电影点击事件 - 完全直通设计
     * 发送电影点击事件，由UI层使用MovieClickHandler处理
     */
    fun onMovieClick(movie: Vod) {
        val flowId = VodFlowTracker.generateFlowId()
        Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_VM_CLICK] 电影点击: ${movie.vodName}")
        Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_VM_INFO] vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")
        VodFlowTracker.logFlowStep(flowId, "MOVIE_VM_CLICK", "ViewModel处理电影点击: ${movie.vodName}")

        // 🔥 完全直通设计：发送电影点击事件，由UI层使用MovieClickHandler处理
        // 不再通过RepositoryAdapter中转
        try {
            Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_EVENT] 发送电影点击事件")
            EventBus.getDefault().post(MovieClickEvent(movie, flowId))
            VodFlowTracker.logFlowStep(flowId, "MOVIE_CLICK_EVENT", "已发送电影点击事件")
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_ERROR] 发送电影点击事件失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(flowId, "MOVIE_CLICK_ERROR", "事件发送失败: ${e.message}")
        }
    }

    /**
     * 刷新配置和内容
     */
    fun refresh() {
        Log.d("ONETV_MOVIE", "🔄 用户触发刷新，强制更新配置")
        viewModelScope.launch {
            // ✅ 保留showWelcomeScreen状态，避免被重置
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
                // showWelcomeScreen保持不变
            )

            try {
                // ✅ 通过适配器强制刷新配置 - 解析逻辑在FongMi_TV中
                repositoryAdapter.refreshConfig()
                Log.d("ONETV_MOVIE", "✅ 配置刷新请求已发送，重新加载首页数据")
                loadHomeData()
            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "💥 刷新过程异常", e)
                loadHomeData() // 降级处理
            }
        }
    }

    /**
     * 设置欢迎界面状态
     */
    fun setWelcomeScreenState(showWelcome: Boolean) {
        Log.d(TAG, "🔄 设置欢迎界面状态: $showWelcome")
        viewModelScope.launch(Dispatchers.Main.immediate) {
            try {
                val currentState = _uiState.value
                if (currentState.showWelcomeScreen != showWelcome) {
                    val newState = currentState.copy(
                        showWelcomeScreen = showWelcome,
                        isLoading = false,
                        error = null
                    )
                    _uiState.value = newState
                    Log.d(TAG, "✅ 欢迎界面状态更新完成: showWelcomeScreen=$showWelcome")
                } else {
                    Log.d(TAG, "⚠️ 欢迎界面状态已经是: $showWelcome，跳过更新")
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ 设置欢迎界面状态失败", e)
            }
        }
    }

    /**
     * 🔥 原版FongMi_TV线路切换逻辑 - 100%移植
     * 基于原版FongMi_TV的线路切换机制，确保稳定性和可靠性
     */
    fun selectRoute(routeUrl: VodConfigUrl) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: VodFlowTracker.generateFlowId()

        // 🔥 原版FongMi_TV关键步骤：立即记录CONFIG_INPUT
        VodFlowTracker.logFlowStart(currentFlowId, "线路切换", "用户选择线路: ${routeUrl.name}")
        VodFlowTracker.logFlowStep(currentFlowId, "CONFIG_INPUT", "用户输入配置URL: ${routeUrl.url}")
        VodFlowTracker.logFlowStep(currentFlowId, "SELECT_ROUTE_INFO", "线路信息 - 名称: ${routeUrl.name}, URL: ${routeUrl.url}")

        viewModelScope.launch {
            // 🔥 原版并发控制：使用互斥锁确保同时只有一个线路切换操作
            routeSwitchMutex.withLock {
                if (isRouteSwitching) {
                    Log.w(TAG, "[FlowID:$currentFlowId] [SELECT_ROUTE_CONCURRENT] 正在切换线路，忽略重复请求")
                    return@withLock
                }
                isRouteSwitching = true
            }

            try {
                // 🔥 第一步：立即关闭线路选择器，防止UI状态冲突
                _uiState.value = _uiState.value.copy(
                    showRouteSelector = false
                )

                // 🔥 第二步：短暂延迟确保UI状态更新完成
                delay(50)

                // 🔥 第三步：显示欢迎页面并开始加载新线路配置
                _uiState.value = _uiState.value.copy(
                    showWelcomeScreen = true,
                    isLoading = true,
                    isLoadingConfig = true,
                    isLoadingRecommend = false,
                    configLoadProgress = 0f,
                    recommendLoadProgress = 0f,
                    error = null,
                    loadingMessage = "正在切换线路：${routeUrl.name}",
                    selectedRoute = routeUrl
                )

                Log.d(TAG, "[FlowID:$currentFlowId] [SELECT_ROUTE_LOADING] 开始线路切换加载流程")
                VodFlowTracker.logFlowStep(currentFlowId, "SELECT_ROUTE_LOADING", "开始线路切换加载")

                // 🔥 第四步：调用原版线路切换逻辑
                repositoryAdapter.switchRoute(routeUrl.url)

                VodFlowTracker.logFlowStep(currentFlowId, "SELECT_ROUTE_COMPLETE", "线路切换请求已发送，等待ConfigUpdateEvent处理")

            } catch (e: CancellationException) {
                Log.d(TAG, "[FlowID:$currentFlowId] [SELECT_ROUTE_CANCELLED] 线路选择被取消")
                throw e
            } catch (e: Exception) {
                VodFlowTracker.logFlowError(currentFlowId, "SELECT_ROUTE", "线路切换失败: ${e.message}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showWelcomeScreen = false,
                    error = "线路切换失败: ${e.message}"
                )
                // 🔥 异常情况下立即重置状态
                routeSwitchMutex.withLock {
                    isRouteSwitching = false
                }
                Log.d(TAG, "[FlowID:$currentFlowId] [SELECT_ROUTE_ERROR_RESET] 线路切换异常，重置状态")
            } finally {
                // 🔥 原版FongMi_TV逻辑：只在异常情况下重置状态，正常情况下等待ConfigUpdateEvent处理
                if (coroutineContext[Job]?.isCancelled == true) {
                    routeSwitchMutex.withLock {
                        isRouteSwitching = false
                    }
                    Log.d(TAG, "[FlowID:$currentFlowId] [SELECT_ROUTE_CANCELLED_RESET] 线路切换被取消，重置状态")
                }
                // 注意：正常情况下不在这里重置isRouteSwitching，等待onConfigUpdateEvent处理
            }
        }
    }

    /**
     * 🔥 原版FongMi_TV线路选择器逻辑
     * 显示线路选择器，获取可用线路列表
     */
    fun showRouteSelector() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: VodFlowTracker.generateFlowId()
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_ROUTE_SELECTOR", "显示线路选择器")

        try {
            // 🔥 获取可用的线路列表
            val availableRoutes = getAvailableRoutes()

            if (availableRoutes.isEmpty()) {
                VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_WARNING", "没有可用线路")
                return
            }

            // 🔥 更新UI状态显示线路选择器
            _uiState.value = _uiState.value.copy(
                showRouteSelector = true,
                availableRoutes = availableRoutes
            )

            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_ROUTE_SELECTOR_SUCCESS",
                "线路选择器显示成功，线路数量: ${availableRoutes.size}")
        } catch (e: Exception) {
            VodFlowTracker.logFlowError(currentFlowId, "SHOW_ROUTE_SELECTOR", "显示线路选择器失败: ${e.message}", e)
        }
    }

    /**
     * 🔥 获取可用的线路列表 - 原版FongMi_TV逻辑
     */
    private fun getAvailableRoutes(): List<VodConfigUrl> {
        val routes = mutableListOf<VodConfigUrl>()

        try {
            // 🔥 从FongMi_TV的Config系统获取真实的线路配置
            val configs = top.cywin.onetv.movie.bean.Config.findUrls()

            for (config in configs) {
                if (!config.url.isNullOrEmpty()) {
                    val name = if (config.name.isNullOrEmpty()) {
                        "线路 ${routes.size + 1}"
                    } else {
                        config.name
                    }

                    val route = VodConfigUrl(name, config.url, false)
                    routes.add(route)
                }
            }
        } catch (e: Exception) {
            VodFlowTracker.logFlowError(VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN",
                "ROUTE_LOAD", "从配置加载线路失败: ${e.message}", e)
        }

        return routes
    }

    /**
     * 隐藏线路选择器
     */
    fun hideRouteSelector() {
        _uiState.value = _uiState.value.copy(showRouteSelector = false)
    }

    // 🔥 switchRoute方法已删除，UI应该调用selectRoute方法

    /**
     * 显示站点选择器
     */
    fun showSiteSelector(sites: List<top.cywin.onetv.movie.bean.Site>) {
        _uiState.value = _uiState.value.copy(
            showSiteSelector = true,
            availableSites = sites
        )
    }



    // ===== 新增功能方法 =====





    /**
     * 加载站点列表
     */
    fun loadSiteList() {
        Log.d(TAG, "🌐 开始加载站点列表")
        viewModelScope.launch {
            try {
                repositoryAdapter.getSiteList()
                Log.d(TAG, "✅ 站点列表加载请求已发送")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 加载站点列表失败", e)
            }
        }
    }

    /**
     * 切换站点 - 修复：只有用户主动选择不同站点才触发加载页面
     */
    fun switchSite(siteKey: String) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SWITCH_SITE] 用户主动选择站点: $siteKey")
        VodFlowTracker.logFlowStep(currentFlowId, "SWITCH_SITE", "用户选择站点: $siteKey")

        // 🔥 修复：检查是否选择的是当前站点，避免重复加载
        val currentSite = _uiState.value.currentSite
        if (currentSite != null && currentSite.key == siteKey) {
            Log.d(TAG, "[FlowID:$currentFlowId] [SWITCH_SITE_SKIP] 选择相同站点，跳过切换: ${currentSite.name}")
            VodFlowTracker.logFlowStep(currentFlowId, "SWITCH_SITE_SKIP", "相同站点跳过: ${currentSite.name}")
            return
        }

        viewModelScope.launch {
            try {
                // 🔥 修复：先隐藏站点选择器
                _uiState.value = _uiState.value.copy(showSiteSelector = false)

                // 🔥 修复：显示欢迎页面并开始加载新站点配置
                _uiState.value = _uiState.value.copy(
                    showWelcomeScreen = true,
                    isLoading = true,
                    isLoadingConfig = true,
                    isLoadingRecommend = false,
                    configLoadProgress = 0f,
                    recommendLoadProgress = 0f,
                    loadingMessage = "正在切换站点并加载配置文件...",
                    error = null
                )

                Log.d(TAG, "[FlowID:$currentFlowId] [SWITCH_SITE_LOADING] 开始切换站点加载流程")
                VodFlowTracker.logFlowStep(currentFlowId, "SWITCH_SITE_LOADING", "开始站点切换加载")

                // 通过适配器切换站点
                repositoryAdapter.switchSite(siteKey)
                Log.d(TAG, "[FlowID:$currentFlowId] [SWITCH_SITE_SUCCESS] 站点切换请求已发送")
                VodFlowTracker.logFlowStep(currentFlowId, "SWITCH_SITE_SUCCESS", "站点切换成功")
            } catch (e: Exception) {
                Log.e(TAG, "[FlowID:$currentFlowId] [SWITCH_SITE_ERROR] 切换站点失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "SWITCH_SITE_ERROR", "站点切换失败: ${e.message}")

                // 显示错误状态
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isLoadingConfig = false,
                    error = "切换站点失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 加载推荐内容
     */
    fun loadRecommendContent() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🌟 加载推荐内容")

                // ✅ 通过适配器获取推荐内容
                repositoryAdapter.getRecommendContent()

                Log.d("ONETV_MOVIE", "✅ 推荐内容请求已发送")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "推荐内容加载失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "推荐内容加载失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 加载分类列表
     */
    fun loadCategories() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "📂 加载分类列表")

                // ✅ 通过适配器获取分类列表
                repositoryAdapter.getCategories()

                Log.d("ONETV_MOVIE", "✅ 分类列表请求已发送")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "分类列表加载失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "分类列表加载失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🔄 刷新数据")

                // ✅ 保留showWelcomeScreen状态，避免被重置
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null
                    // showWelcomeScreen保持不变
                )

                // ✅ 通过适配器刷新配置
                repositoryAdapter.refreshConfig()

                // 重新加载首页数据
                loadHomeData()

                Log.d("ONETV_MOVIE", "✅ 数据刷新完成")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "数据刷新失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据刷新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清空缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🗑️ 清空缓存")

                // ✅ 通过适配器清空缓存
                repositoryAdapter.clearAllCache()

                Log.d("ONETV_MOVIE", "✅ 缓存清空完成")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "缓存清空失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "缓存清空失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    // ===== 私有方法 =====

    /**
     * 处理配置更新成功
     */
    private fun handleConfigUpdateSuccess(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d(TAG, "✅ 处理配置更新成功")

        // ✅ 检查配置是否有效
        if (config.sites.isEmpty()) {
            Log.w(TAG, "⚠️ 配置文件中没有站点数据")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "配置文件无效，没有可用的站点"
            )
            return
        }

        Log.d(TAG, "✅ 配置文件加载成功，站点数: ${config.sites.size}")

        // ✅ 检查是否为仓库索引文件
        val isStoreHouse = checkIfStoreHouseIndex(config)
        if (isStoreHouse) {
            Log.d(TAG, "🏪 检测到仓库索引文件")
            viewModelScope.launch {
                handleStoreHouseIndex(config)
            }
            return
        }

        // ✅ 加载正常配置
        Log.d(TAG, "📺 加载正常配置文件")
        viewModelScope.launch {
            loadNormalConfig(config)

            // ✅ 配置加载完成，但继续在欢迎页面加载推荐内容
            Log.d(TAG, "🔄 配置加载完成，继续加载推荐内容")
            Log.d("VOD_FLOW", "[CONFIG_SUCCESS_CONTINUE] 配置加载成功，继续在欢迎页面加载推荐内容")
            val currentState = _uiState.value
            _uiState.value = currentState.copy(
                isLoading = true,              // 继续加载状态
                isLoadingConfig = false,       // 配置加载完成
                isLoadingRecommend = true,     // 开始推荐内容加载
                configLoadProgress = 1f,       // 配置加载100%
                recommendLoadProgress = 0f,    // 推荐内容加载0%
                error = null,
                loadingMessage = "正在加载推荐内容...",
                showWelcomeScreen = true       // ✅ 继续显示欢迎页面
            )
            Log.d(TAG, "✅ UI状态更新完成: isLoadingConfig=${_uiState.value.isLoadingConfig}, isLoadingRecommend=${_uiState.value.isLoadingRecommend}")
            Log.d("VOD_FLOW", "[CONFIG_SUCCESS_CONTINUE_WELCOME] 配置成功，继续在欢迎页面加载推荐内容")
        }

        // ✅ 重置配置更新标志
        isHandlingConfigUpdate = false
        Log.d(TAG, "✅ 配置更新处理完成，重置标志")
    }

    /**
     * 处理首页内容成功
     */
    private fun handleHomeContentSuccess(
        categories: List<top.cywin.onetv.movie.bean.Class>,
        recommendVods: List<top.cywin.onetv.movie.bean.Vod>
    ) {
        Log.d(TAG, "✅ 处理首页内容成功: 分类${categories.size}个, 推荐${recommendVods.size}个")

        // 转换分类数据
        val categoryInfos = categories.map { clazz ->
            Log.d(TAG, "🔄 转换分类: ${clazz.typeName} (${clazz.typeId})")
            ViewModelAdapter.convertClassToCategory(clazz)
        }.filterNotNull()

        // 转换推荐电影数据
        val recommendMovies = recommendVods.map { vod ->
            Log.d(TAG, "🔄 转换推荐电影: ${vod.vodName}")
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        Log.d(TAG, "📊 转换后分类数量: ${categoryInfos.size}")
        Log.d(TAG, "📊 转换后推荐电影数量: ${recommendMovies.size}")

        _uiState.value = _uiState.value.copy(
            categories = categoryInfos,
            recommendMovies = recommendMovies,
            isLoading = false,
            isLoadingConfig = false,
            isLoadingRecommend = false,
            configLoadProgress = 1f,
            recommendLoadProgress = 1f,
            error = null,
            loadingMessage = "",
            showWelcomeScreen = false  // ✅ 所有内容加载成功后关闭欢迎页面，进入完整主界面
        )

        Log.d(TAG, "✅ UI状态已更新")
        Log.d("VOD_FLOW", "[HOME_CONTENT_SUCCESS] 所有内容加载成功，关闭欢迎页面，进入完整主界面")

        // 加载各分类的内容
        categories.take(6).forEach { category ->
            Log.d(TAG, "🔄 加载分类内容: ${category.typeName}")
            repositoryAdapter.getContentList(category.typeId, 1, emptyMap())
        }
    }

    /**
     * 处理分类内容更新
     */
    private fun handleCategoryContentUpdate(event: CategoryContentEvent) {
        // 转换电影数据
        val movieItems = event.vods.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 🔥 修复逻辑：检查是否是分类点击触发的内容加载
        val currentCategoryName = _uiState.value.currentCategoryName
        if (currentCategoryName != null) {
            // 这是分类点击触发的，显示在主要区域
            Log.d(TAG, "📂 [修复逻辑] 分类点击内容更新: $currentCategoryName, 电影数: ${movieItems.size}")

            val existingMovies = if (event.page == 1) emptyList() else _uiState.value.categoryMovies
            val allMovies = existingMovies + movieItems

            _uiState.value = _uiState.value.copy(
                isLoading = false,
                categoryMovies = allMovies,
                error = null
            )
        } else {
            // 这是正常的首页分类内容加载
            val currentCategories = _uiState.value.homeCategories.toMutableList()
            val existingIndex = currentCategories.indexOfFirst { it.categoryId == event.typeId }

            if (existingIndex >= 0) {
                // 更新现有分类
                val existingCategory = currentCategories[existingIndex]
                currentCategories[existingIndex] = existingCategory.copy(
                    movies = if (event.page == 1) movieItems else existingCategory.movies + movieItems,
                    hasMore = event.hasMore,
                    isLoading = false
                )
            } else {
                // 添加新分类 - 通过RepositoryAdapter获取分类信息
                val categoryName = repositoryAdapter.getCategoryName(event.typeId) ?: "未知分类"
                currentCategories.add(
                    HomeCategorySection(
                        categoryId = event.typeId,
                        categoryName = categoryName,
                        movies = movieItems,
                        hasMore = event.hasMore,
                        isLoading = false
                    )
                )
            }

            _uiState.value = _uiState.value.copy(
                homeCategories = currentCategories
            )
        }
    }







    // ===== 历史记录管理方法 =====

    /**
     * 加载观看历史
     */
    fun loadWatchHistory() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoadingHistory = true, error = null)
                Log.d("VOD_FLOW", "[LOAD_WATCH_HISTORY] 开始加载观看历史")

                val histories = withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的History系统获取历史记录
                    val historyList = top.cywin.onetv.movie.bean.History.get()
                    Log.d("VOD_FLOW", "[LOAD_WATCH_HISTORY] 从数据库获取到 ${historyList.size} 条历史记录")

                    historyList.mapNotNull { history ->
                        try {
                            // ✅ 修复：包含海报URL和完整的观看时长信息
                            val vodPic = history.vodPic ?: ""
                            val position = history.position
                            val duration = history.duration
                            val progressPercent = if (duration > 0) (position.toFloat() / duration * 100).toInt() else 0

                            // 🔥 修复：安全获取vodId和siteKey，避免ArrayIndexOutOfBoundsException
                            val vodId = history.getVodId()
                            val siteKey = history.getSiteKey()

                            // 🔥 如果vodId或siteKey为空，跳过这条记录
                            if (vodId.isEmpty() || siteKey.isEmpty()) {
                                Log.w("VOD_FLOW", "[HISTORY_SKIP] 跳过无效历史记录: key=${history.key}, vodName=${history.vodName}")
                                return@mapNotNull null
                            }

                            Log.d("VOD_FLOW", "[HISTORY_ITEM] ${history.vodName}: 海报=$vodPic, 进度=${formatTime(position)}/${formatTime(duration)} (${progressPercent}%)")

                            WatchHistory(
                                vodId = vodId, // 🔥 修复：使用安全获取的vodId
                                vodName = history.vodName ?: "",
                                vodPic = vodPic, // ✅ 添加海报URL
                                siteKey = siteKey, // 🔥 修复：使用安全获取的siteKey
                                episodeName = history.vodRemarks ?: "",
                                position = position,
                                duration = duration,
                                watchTime = history.createTime,
                                isCompleted = position >= duration * 0.9
                            )
                        } catch (e: Exception) {
                            Log.e("VOD_FLOW", "[HISTORY_ITEM_ERROR] 处理历史记录失败: ${history.vodName}", e)
                            null // 跳过有问题的记录
                        }
                    }
                }

                _uiState.value = _uiState.value.copy(
                    isLoadingHistory = false,
                    watchHistories = histories,
                    error = null
                )

                Log.d("VOD_FLOW", "[LOAD_WATCH_HISTORY_SUCCESS] 观看历史加载完成: ${histories.size}条")

            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[LOAD_WATCH_HISTORY_ERROR] 观看历史加载失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoadingHistory = false,
                    error = "加载观看历史失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 格式化时间显示（毫秒转为HH:MM:SS格式）
     */
    private fun formatTime(timeMs: Long): String {
        if (timeMs <= 0) return "00:00:00"

        val totalSeconds = timeMs / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60

        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    /**
     * 删除单条观看历史
     * 🔥 修复：需要传递完整的WatchHistory对象，而不是只传递vodId
     */
    fun deleteWatchHistory(history: WatchHistory) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY] 开始删除观看历史: ${history.vodId}, 站点: ${history.siteKey}")
        VodFlowTracker.logFlowStep(currentFlowId, "DELETE_WATCH_HISTORY", "删除观看历史: ${history.vodId}")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // 🔥 修复：使用正确的History key格式进行删除
                    val siteKey = history.siteKey
                    val vodId = history.vodId
                    val cid = top.cywin.onetv.movie.api.config.VodConfig.getCid()

                    // 构建正确的History key：站点@@@vodId@@@cid
                    val historyKey = siteKey + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId +
                        top.cywin.onetv.movie.database.AppDatabase.SYMBOL + cid

                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY_KEY] 删除History key: $historyKey")

                    val historyRecord = top.cywin.onetv.movie.bean.History.find(historyKey)
                    if (historyRecord != null) {
                        historyRecord.delete()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY_FOUND] 找到并删除历史记录: $historyKey")
                    } else {
                        Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY_NOT_FOUND] 未找到历史记录: $historyKey")
                    }
                }

                // 重新加载历史记录
                loadWatchHistory()

                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY_SUCCESS] 观看历史删除成功: ${history.vodId}")
                VodFlowTracker.logFlowStep(currentFlowId, "DELETE_WATCH_HISTORY_SUCCESS", "观看历史删除成功")

            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_WATCH_HISTORY_ERROR] 观看历史删除失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "DELETE_WATCH_HISTORY_ERROR", "观看历史删除失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    error = "删除观看历史失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清空所有观看历史
     */
    fun clearAllWatchHistory() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CLEAR_ALL_WATCH_HISTORY] 开始清空所有观看历史")
        VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_WATCH_HISTORY", "清空所有观看历史")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的History系统清空所有历史记录
                    top.cywin.onetv.movie.bean.History.delete(top.cywin.onetv.movie.api.config.VodConfig.getCid())
                }

                _uiState.value = _uiState.value.copy(
                    watchHistories = emptyList(),
                    error = null
                )

                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CLEAR_ALL_WATCH_HISTORY_SUCCESS] 所有观看历史清空成功")
                VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_WATCH_HISTORY_SUCCESS", "所有观看历史清空成功")

            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [CLEAR_ALL_WATCH_HISTORY_ERROR] 清空观看历史失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_WATCH_HISTORY_ERROR", "清空观看历史失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    error = "清空观看历史失败: ${e.message}"
                )
            }
        }
    }

    // ===== 收藏管理方法 =====

    /**
     * 加载收藏列表 - 通过适配器调用FongMi_TV功能
     */
    fun loadFavorites() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [LOAD_FAVORITES] 开始加载收藏列表")
        VodFlowTracker.logFlowStep(currentFlowId, "LOAD_FAVORITES", "加载收藏列表")

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoadingFavorites = true, error = null)

                val favorites = withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的Keep系统获取收藏列表
                    top.cywin.onetv.movie.database.AppDatabase.get().getKeepDao().getVod()
                }

                _uiState.value = _uiState.value.copy(
                    favoritesList = favorites,
                    isLoadingFavorites = false
                )

                Log.d(TAG, "[FlowID:$currentFlowId] [LOAD_FAVORITES_SUCCESS] 收藏列表加载成功，数量: ${favorites.size}")
                VodFlowTracker.logFlowStep(currentFlowId, "LOAD_FAVORITES_SUCCESS", "收藏列表加载成功")
            } catch (e: Exception) {
                Log.e(TAG, "[FlowID:$currentFlowId] [LOAD_FAVORITES_ERROR] 收藏列表加载失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "LOAD_FAVORITES_ERROR", "收藏列表加载失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoadingFavorites = false,
                    error = "加载收藏列表失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 删除单条收藏 - 通过适配器调用FongMi_TV功能
     */
    fun deleteFavorite(key: String) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [DELETE_FAVORITE] 开始删除收藏: $key")
        VodFlowTracker.logFlowStep(currentFlowId, "DELETE_FAVORITE", "删除收藏")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的Keep系统删除收藏
                    // 🔥 修复：使用正确的删除方法，需要cid和完整的key
                    val cid = top.cywin.onetv.movie.api.config.VodConfig.getCid()
                    top.cywin.onetv.movie.database.AppDatabase.get().getKeepDao().delete(cid, key)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DELETE_FAVORITE_DETAIL] 删除收藏: cid=$cid, key=$key")
                }

                // 重新加载收藏列表
                loadFavorites()

                Log.d(TAG, "[FlowID:$currentFlowId] [DELETE_FAVORITE_SUCCESS] 收藏删除成功")
                VodFlowTracker.logFlowStep(currentFlowId, "DELETE_FAVORITE_SUCCESS", "收藏删除成功")
            } catch (e: Exception) {
                Log.e(TAG, "[FlowID:$currentFlowId] [DELETE_FAVORITE_ERROR] 收藏删除失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "DELETE_FAVORITE_ERROR", "收藏删除失败: ${e.message}")
                _uiState.value = _uiState.value.copy(error = "删除收藏失败: ${e.message}")
            }
        }
    }

    /**
     * 清空所有收藏 - 通过适配器调用FongMi_TV功能
     */
    fun clearAllFavorites() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [CLEAR_ALL_FAVORITES] 开始清空所有收藏")
        VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_FAVORITES", "清空所有收藏")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的Keep系统清空所有收藏
                    val favorites = top.cywin.onetv.movie.database.AppDatabase.get().getKeepDao().getVod()
                    favorites.forEach { favorite ->
                        val parts = favorite.key.split(top.cywin.onetv.movie.database.AppDatabase.SYMBOL)
                        if (parts.size >= 2) {
                            val vodId = parts[1]
                            top.cywin.onetv.movie.bean.Keep.delete(vodId)
                        }
                    }
                }

                // 重新加载收藏列表
                loadFavorites()

                Log.d(TAG, "[FlowID:$currentFlowId] [CLEAR_ALL_FAVORITES_SUCCESS] 所有收藏清空成功")
                VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_FAVORITES_SUCCESS", "所有收藏清空成功")
            } catch (e: Exception) {
                Log.e(TAG, "[FlowID:$currentFlowId] [CLEAR_ALL_FAVORITES_ERROR] 清空收藏失败: ${e.message}", e)
                VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_ALL_FAVORITES_ERROR", "清空收藏失败: ${e.message}")
                _uiState.value = _uiState.value.copy(error = "清空收藏失败: ${e.message}")
            }
        }
    }

    /**
     * 🔥 原版FongMi_TV直接导航：移除StateFlow导航机制
     * 现在采用原版直接导航：RepositoryAdapter → 直接navController.navigate()
     *
     * 注释掉原有的NavigationEvent监听，因为现在RepositoryAdapter直接处理导航
     */
    /*
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNavigationEvent(event: NavigationEvent.NavigateToDetail) {
        Log.d(TAG, "📡 [原版直接导航] 已移除StateFlow导航机制")
        // 现在RepositoryAdapter直接处理导航，不再需要通过StateFlow传递
    }
    */

    // ===== 🔥 新增：首页顶端功能按钮实现 - 通过适配器调用FongMi_TV功能 =====

    /**
     * 🔥 修复：返回直播界面 - 直接返回到TV应用主模块的主界面
     * 注意：此方法已废弃，直播返回逻辑应直接在UI层使用navController.popBackStack()
     */
    @Deprecated("直播返回逻辑应直接在UI层使用navController.popBackStack()")
    fun navigateToLive() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [NAVIGATE_TO_LIVE] 返回直播界面（已废弃方法）")
        VodFlowTracker.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE", "返回直播界面（已废弃方法）")

        try {
            // 🔥 修复：直播返回逻辑应该在UI层直接使用navController.popBackStack()
            // 不再调用适配器方法，因为直播返回应该直接退出Movie模块
            Log.w(TAG, "[FlowID:$currentFlowId] [NAVIGATE_TO_LIVE_DEPRECATED] 此方法已废弃，请在UI层直接使用navController.popBackStack()")
            VodFlowTracker.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE_DEPRECATED", "方法已废弃，应使用navController.popBackStack()")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [NAVIGATE_TO_LIVE_ERROR] 返回直播界面失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE_ERROR", "返回直播失败: ${e.message}")
        }
    }

    /**
     * 显示观看历史界面 - 通过适配器调用FongMi_TV功能
     */
    fun showHistoryScreen() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_HISTORY] 开始显示观看历史")
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_HISTORY", "显示观看历史")

        try {
            // 🔥 通过适配器调用FongMi_TV的历史功能
            repositoryAdapter.showHistoryScreen()
            Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_HISTORY_SUCCESS] 显示观看历史成功")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [SHOW_HISTORY_ERROR] 显示观看历史失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_HISTORY_ERROR", "观看历史失败: ${e.message}")
        }
    }

    /**
     * 显示收藏界面 - 通过适配器调用FongMi_TV功能
     */
    fun showFavoritesScreen() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_FAVORITES] 开始显示收藏界面")
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_FAVORITES", "显示收藏界面")

        try {
            // 🔥 通过适配器调用FongMi_TV的收藏功能
            repositoryAdapter.showFavoritesScreen()
            Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_FAVORITES_SUCCESS] 显示收藏界面成功")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [SHOW_FAVORITES_ERROR] 显示收藏界面失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_FAVORITES_ERROR", "收藏界面失败: ${e.message}")
        }
    }

    /**
     * 显示站点选择器 - 修复：只显示选择器，不立即触发加载
     */
    fun showSiteSelector() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_SITE_SELECTOR] 显示站点选择器，不触发加载")
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR", "显示站点选择器")

        try {
            // 🔥 修复：通过适配器获取站点列表，但不立即加载
            repositoryAdapter.showSiteSelector()
            Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_SITE_SELECTOR_SUCCESS] 站点选择器显示成功")
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR_SUCCESS", "站点选择器显示成功")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [SHOW_SITE_SELECTOR_ERROR] 显示站点选择器失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR_ERROR", "站点选择器失败: ${e.message}")
        }
    }

    /**
     * 显示搜索界面 - 通过适配器调用FongMi_TV功能
     */
    fun showSearchScreen() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_SEARCH] 开始显示搜索界面")
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_SEARCH", "显示搜索界面")

        try {
            // 🔥 通过适配器调用FongMi_TV的搜索功能
            repositoryAdapter.showSearchScreen()
            Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_SEARCH_SUCCESS] 显示搜索界面成功")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [SHOW_SEARCH_ERROR] 显示搜索界面失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_SEARCH_ERROR", "搜索界面失败: ${e.message}")
        }
    }



    /**
     * 🔥 显示路线选择器 - 接受线路列表参数
     */
    fun showRouteSelector(routes: List<VodConfigUrl>) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_ROUTE_SELECTOR_WITH_ROUTES] 显示路线选择器，线路数量: ${routes.size}")
        VodFlowTracker.logFlowStep(currentFlowId, "SHOW_ROUTE_SELECTOR_WITH_ROUTES", "显示路线选择器，线路数量: ${routes.size}")

        try {
            // 🔥 修复：获取当前配置的URL，找到对应的选中线路
            val currentConfigUrl = vodConfig.config?.url ?: ""
            val currentSelectedRoute = routes.find { it.url == currentConfigUrl }

            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_CURRENT] 当前配置URL: $currentConfigUrl")
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SELECTED] 找到选中线路: ${currentSelectedRoute?.name ?: "未找到"}")

            // 🔥 更新UI状态，显示线路选择器并设置当前选中的线路
            _uiState.value = _uiState.value.copy(
                showRouteSelector = true,
                availableRoutes = routes,
                selectedRoute = currentSelectedRoute // 🔥 修复：设置当前选中的线路
            )
            Log.d(TAG, "[FlowID:$currentFlowId] [SHOW_ROUTE_SELECTOR_WITH_ROUTES_SUCCESS] 路线选择器UI显示成功")
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [SHOW_ROUTE_SELECTOR_WITH_ROUTES_ERROR] 路线选择器UI显示失败: ${e.message}", e)
            VodFlowTracker.logFlowStep(currentFlowId, "SHOW_ROUTE_SELECTOR_WITH_ROUTES_ERROR", "路线选择器UI失败: ${e.message}")
        }
    }



    /**
     * 隐藏站点选择器
     */
    fun hideSiteSelector() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [HIDE_SITE_SELECTOR] 隐藏站点选择器")

        _uiState.value = _uiState.value.copy(
            showSiteSelector = false
        )
    }

    /**
     * 🔥 处理ConfigUpdateEvent事件 - 按照原版FongMi_TV机制处理配置更新
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onConfigUpdateEvent(event: top.cywin.onetv.movie.event.ConfigUpdateEvent) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d(TAG, "[FlowID:$currentFlowId] [CONFIG_UPDATE_EVENT] 收到配置更新事件: ${event}")
        VodFlowTracker.logFlowStep(currentFlowId, "CONFIG_UPDATE_EVENT", "配置更新: success=${event.isSuccess}")

        try {
            if (event.isSuccess) {
                Log.d(TAG, "[FlowID:$currentFlowId] [CONFIG_UPDATE_SUCCESS] 配置更新成功，站点数: ${event.config?.sites?.size ?: 0}")

                // 🔥 原版FongMi_TV配置更新处理逻辑
                if (isRouteSwitching) {
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_ROUTE_SWITCH] 线路切换完成，重新加载首页数据")
                    viewModelScope.launch {
                        try {
                            // 🔥 先更新进度到100%，显示完成状态
                            _uiState.value = _uiState.value.copy(
                                loadingMessage = "线路切换完成，正在加载首页数据",
                                configLoadProgress = 1f
                            )
                            delay(500) // 让用户看到100%进度

                            // 重新加载首页数据
                            loadHomeData()
                            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_RELOAD] 线路切换后重新加载首页数据完成")

                            // 🔥 隐藏欢迎页面，显示首页内容，重置所有加载状态
                            _uiState.value = _uiState.value.copy(
                                showWelcomeScreen = false,
                                isLoading = false,
                                isLoadingConfig = false,
                                isLoadingRecommend = false,
                                configLoadProgress = 1f,
                                recommendLoadProgress = 1f,
                                loadingMessage = ""
                            )

                            // 🔥 重置线路切换状态，允许下次切换
                            routeSwitchMutex.withLock {
                                isRouteSwitching = false
                            }

                            // 🔥 使用VodFlowTracker记录线路切换完成
                            VodFlowTracker.logFlowComplete(currentFlowId, "线路切换", "线路切换成功完成，首页数据已重新加载")

                        } catch (e: Exception) {
                            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_RELOAD_ERROR] 线路切换后重新加载失败: ${e.message}", e)
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                showWelcomeScreen = false,
                                error = "线路切换后加载失败: ${e.message}"
                            )
                            // 🔥 即使失败也要重置状态
                            routeSwitchMutex.withLock {
                                isRouteSwitching = false
                            }
                        }
                    }
                } else {
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_SKIP] 非线路切换的配置更新，跳过重新加载")
                }
            } else {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_ERROR] 配置更新失败: ${event.errorMessage}")

                // 🔥 原版FongMi_TV错误处理逻辑
                if (isRouteSwitching) {
                    viewModelScope.launch {
                        routeSwitchMutex.withLock {
                            isRouteSwitching = false
                        }
                    }
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CONFIG_UPDATE_ERROR_RESET] 配置更新失败，重置线路切换状态")
                }

                // 显示错误信息
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showWelcomeScreen = false,
                    error = "配置更新失败: ${event.errorMessage}"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "[FlowID:$currentFlowId] [CONFIG_UPDATE_EVENT_ERROR] 处理配置更新事件失败: ${e.message}", e)
        }
    }
}
