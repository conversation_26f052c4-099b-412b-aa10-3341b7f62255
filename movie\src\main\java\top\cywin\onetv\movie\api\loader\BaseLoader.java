package top.cywin.onetv.movie.api.loader;

import android.text.TextUtils;

import top.cywin.onetv.movie.api.config.LiveConfig;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.bean.Live;
import top.cywin.onetv.movie.bean.Site;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderNull;
import com.github.catvod.utils.Util;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import dalvik.system.DexClassLoader;

public class BaseLoader {

    private final JarLoader jarLoader;
    private final PyLoader pyLoader;
    private final JsLoader jsLoader;
    // 🔥 原版FongMi_TV FlowID支持 - 100%移植
    private String currentFlowId;

    // 🔥 原版FongMi_TV：简化同步机制，移除复杂的等待逻辑
    private final Object spiderLock = new Object();
    private volatile boolean isClearing = false;

    private static class Loader {
        static volatile BaseLoader INSTANCE = new BaseLoader();
    }

    public static BaseLoader get() {
        return Loader.INSTANCE;
    }

    private BaseLoader() {
        this.jarLoader = new JarLoader();
        this.pyLoader = new PyLoader();
        this.jsLoader = new JsLoader();
    }

    // 🔥 原版FongMi_TV setFlowId方法 - 100%移植
    public void setFlowId(String flowId) {
        this.currentFlowId = flowId;
        // 传递FlowID给JsLoader（如果支持的话）
        if (this.jsLoader instanceof JsLoader) {
            // JsLoader可能需要FlowID支持，这里预留接口
        }
    }

    // 🔥 100%移植原版FongMi_TV的clear方法 - 简单直接，无等待机制
    public void clear() {
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEAR",
                "开始清理BaseLoader");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEAR] 开始清理BaseLoader");

        // 🔥 关键修复：设置清理标志，防止新的Spider请求
        synchronized (spiderLock) {
            isClearing = true;
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_SET_CLEARING",
                    "设置清理标志，阻止新的Spider请求");
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_SET_CLEARING] 设置清理标志，阻止新的Spider请求");
        }

        // 🔥 原版FongMi_TV清理逻辑：直接清理，不等待
        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEAR_JARS",
                "开始清理JarLoader");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEAR_JARS] 开始清理JarLoader");
        this.jarLoader.clear();

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEAR_PY",
                "开始清理PyLoader");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEAR_PY] 开始清理PyLoader");
        this.pyLoader.clear();

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEAR_JS",
                "开始清理JsLoader");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEAR_JS] 开始清理JsLoader");
        this.jsLoader.clear();

        // 🔥 清理完成后重置标志
        synchronized (spiderLock) {
            isClearing = false;
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_RESET_CLEARING",
                    "重置清理标志，允许新的Spider请求");
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_RESET_CLEARING] 重置清理标志，允许新的Spider请求");
        }

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEAR_SUCCESS",
                "BaseLoader清理完成");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEAR_SUCCESS] BaseLoader清理完成");
    }

    public Spider getSpider(String key, String api, String ext, String jar) {
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        // 🔥 关键修复：检查是否正在清理，防止并发竞争
        synchronized (spiderLock) {
            if (isClearing) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_CLEARING",
                        "BaseLoader正在清理中，返回SpiderNull");
                android.util.Log.w("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [BASE_LOADER_CLEARING] BaseLoader正在清理中，返回SpiderNull");
                return new SpiderNull();
            }
        }

        // 🔥 原版FongMi_TV逻辑：直接执行，不计数
        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_GET_SPIDER",
                "获取Spider: key=" + key + ", api=" + api);
        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_SPIDER] 获取Spider: key=" + key + ", api=" + api);

        boolean js = api.contains(".js");
        boolean py = api.contains(".py");
        boolean csp = api.startsWith("csp_");

        Spider spider;
        if (py) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_GET_PY_SPIDER",
                    "获取Python Spider: " + key);
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_PY_SPIDER] 获取Python Spider: " + key);
            spider = pyLoader.getSpider(key, api, ext);
        } else if (js) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_GET_JS_SPIDER",
                    "获取JavaScript Spider: " + key);
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_JS_SPIDER] 获取JavaScript Spider: " + key);
            spider = jsLoader.getSpider(key, api, ext, jar);
        } else if (csp) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_GET_JAR_SPIDER",
                    "获取Jar Spider: " + key);
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_JAR_SPIDER] 获取Jar Spider: " + key);
            spider = jarLoader.getSpider(key, api, ext, jar);
        } else {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "BASE_LOADER_GET_NULL_SPIDER",
                    "未知Spider类型，返回SpiderNull: " + api);
            android.util.Log.w("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_NULL_SPIDER] 未知Spider类型，返回SpiderNull: " + api);
            spider = new SpiderNull();
        }

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "BASE_LOADER_GET_SPIDER_SUCCESS",
                "Spider获取完成: " + spider.getClass().getSimpleName());
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [BASE_LOADER_GET_SPIDER_SUCCESS] Spider获取完成: "
                + spider.getClass().getSimpleName());

        return spider;
    }

    public Spider getSpider(Map<String, String> params) {
        if (!params.containsKey("siteKey"))
            return new SpiderNull();
        Live live = LiveConfig.get().getLive(params.get("siteKey"));
        Site site = VodConfig.get().getSite(params.get("siteKey"));
        if (!site.isEmpty())
            return site.spider();
        if (!live.isEmpty())
            return live.spider();
        return new SpiderNull();
    }

    public void setRecent(String key, String api, String jar) {
        boolean js = api.contains(".js");
        boolean py = api.contains(".py");
        boolean csp = api.startsWith("csp_");
        if (js)
            jsLoader.setRecent(key);
        else if (py)
            pyLoader.setRecent(key);
        else if (csp)
            jarLoader.setRecent(Util.md5(jar));
    }

    public Object[] proxyLocal(Map<String, String> params) {
        if ("js".equals(params.get("do"))) {
            return jsLoader.proxyInvoke(params);
        } else if ("py".equals(params.get("do"))) {
            return pyLoader.proxyInvoke(params);
        } else {
            return jarLoader.proxyInvoke(params);
        }
    }

    public void parseJar(String jar, boolean recent) {
        if (TextUtils.isEmpty(jar))
            return;
        jarLoader.parseJar(Util.md5(jar), jar);
        if (recent)
            jarLoader.setRecent(Util.md5(jar));
    }

    public DexClassLoader dex(String jar) {
        return jarLoader.dex(jar);
    }

    public JSONObject jsonExt(String key, LinkedHashMap<String, String> jxs, String url) throws Throwable {
        return jarLoader.jsonExt(key, jxs, url);
    }

    public JSONObject jsonExtMix(String flag, String key, String name,
            LinkedHashMap<String, HashMap<String, String>> jxs, String url) throws Throwable {
        return jarLoader.jsonExtMix(flag, key, name, jxs, url);
    }
}
