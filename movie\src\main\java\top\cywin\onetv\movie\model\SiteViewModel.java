package top.cywin.onetv.movie.model;

import android.text.TextUtils;
import android.util.Log;

import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import top.cywin.onetv.movie.App;
import top.cywin.onetv.movie.Constants;
import top.cywin.onetv.movie.R;
import top.cywin.onetv.movie.event.*;
import top.cywin.onetv.movie.event.MovieIdTransformEvent;
import top.cywin.onetv.movie.utils.VodFlowTracker;

import org.greenrobot.eventbus.EventBus;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.bean.Episode;
import top.cywin.onetv.movie.bean.Flag;
import top.cywin.onetv.movie.bean.Result;
import top.cywin.onetv.movie.bean.Site;
import top.cywin.onetv.movie.bean.Url;
import top.cywin.onetv.movie.bean.Vod;
import top.cywin.onetv.movie.exception.ExtractException;
import top.cywin.onetv.movie.player.Source;
import top.cywin.onetv.movie.utils.ResUtil;
import top.cywin.onetv.movie.utils.Sniffer;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.Trans;
import com.github.catvod.utils.Util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Response;

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus;
import top.cywin.onetv.movie.event.*;

public class SiteViewModel extends ViewModel {

    private static final String TAG = "VOD_FLOW";

    public MutableLiveData<Episode> episode;
    public MutableLiveData<Result> result;
    public MutableLiveData<Result> player;
    public MutableLiveData<Result> search;
    public MutableLiveData<Result> action;
    private ExecutorService executor;
    // 🔥 关键修复：播放地址解析使用独立的线程池，避免被其他操作覆盖
    private ExecutorService playerExecutor;

    // ✅ 添加搜索状态记录
    private String lastSearchKeyword = "";

    // 🔥 原版FongMi_TV设计：全局搜索线程池，10个可暂停线程
    private static top.cywin.onetv.movie.utils.PauseExecutor globalSearchExecutor;
    private String lastTypeId = "";
    private int lastPage = 1;
    private boolean isDetailContent = false; // 标识是否为详情内容
    private String currentSiteKey = null; // 当前站点Key，用于设置Vod的siteKey

    // 🔥 新增：播放器操作状态标志，防止并发homeContent调用干扰播放器操作
    private volatile boolean isPlayerOperationActive = false;

    public SiteViewModel() {
        this.episode = new MutableLiveData<>();
        this.result = new MutableLiveData<>();
        this.player = new MutableLiveData<>();
        this.search = new MutableLiveData<>();
        this.action = new MutableLiveData<>();
    }

    public void setEpisode(Episode value) {
        episode.setValue(value);
    }

    public synchronized void homeContent() {
        // 🔥 修复：如果播放器操作正在进行，跳过homeContent调用，防止干扰
        if (isPlayerOperationActive) {
            Log.d(TAG, "⚠️ [并发控制] 播放器操作进行中，跳过homeContent调用，防止干扰播放器操作");
            return;
        }

        // 🔥 修复：放宽并发控制，适配增加的线程池容量
        if (executor != null && !executor.isShutdown()) {
            // 检查是否有正在执行的任务
            if (executor instanceof java.util.concurrent.ThreadPoolExecutor) {
                java.util.concurrent.ThreadPoolExecutor tpe = (java.util.concurrent.ThreadPoolExecutor) executor;
                // 🔥 修复：提高阈值到6个活跃任务，适配8线程池容量
                if (tpe.getActiveCount() > 6) {
                    Log.w(TAG, "⚠️ [并发控制] 有" + tpe.getActiveCount() + "个任务正在执行，跳过homeContent调用");
                    return;
                } else if (tpe.getActiveCount() > 4) {
                    Log.d(TAG, "ℹ️ [并发控制] 有" + tpe.getActiveCount() + "个任务正在执行，但允许继续");
                }
            }
        }

        Log.d(TAG, "🔄 开始获取首页内容");
        // ✅ 重置分类状态，确保事件正确分发
        this.lastTypeId = null;
        this.lastPage = 1;
        execute(result, () -> {
            Site site = VodConfig.get().getHome();
            Log.d(TAG, "🏠 获取默认站点: " + (site != null ? site.getName() : "null"));
            Log.d(TAG, "📊 站点类型: " + (site != null ? site.getType() : "unknown"));

            if (site.getType() == 3) {
                Log.d(TAG, "🕷️ 处理type=3站点，开始获取Spider");
                try {
                    Spider spider = site.recent().spider();
                    Log.d(TAG, "✅ Spider获取成功: " + (spider != null ? spider.getClass().getSimpleName() : "null"));

                    Log.d(TAG, "🔄 调用spider.homeContent(true)");
                    String homeContent = spider.homeContent(true);
                    Log.d(TAG, "📊 homeContent结果长度: " + (homeContent != null ? homeContent.length() : 0));
                    SpiderDebug.log(homeContent);

                    Result result = Result.fromJson(homeContent);
                    Log.d(TAG, "📊 解析结果: " + (result != null ? result.getList().size() : 0) + "个内容");

                    if (!result.getList().isEmpty()) {
                        Log.d(TAG, "✅ homeContent返回了内容，直接返回");
                        return result;
                    }

                    Log.d(TAG, "⚠️ homeContent为空，尝试homeVideoContent");
                    String homeVideoContent = spider.homeVideoContent();
                    Log.d(TAG,
                            "📊 homeVideoContent结果长度: " + (homeVideoContent != null ? homeVideoContent.length() : 0));
                    SpiderDebug.log(homeVideoContent);

                    result.setList(Result.fromJson(homeVideoContent).getList());
                    Log.d(TAG, "📊 最终结果: " + result.getList().size() + "个内容");
                    return result;
                } catch (Exception e) {
                    Log.e(TAG, "❌ Spider处理失败", e);
                    throw e;
                }
            } else if (site.getType() == 4) {
                Log.d(TAG, "🔄 处理type=4站点");
                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("filter", "true");
                String homeContent = call(site.fetchExt(), params);
                Log.d(TAG, "📊 type=4结果长度: " + (homeContent != null ? homeContent.length() : 0));
                SpiderDebug.log(homeContent);
                return Result.fromJson(homeContent);
            } else {
                Log.d(TAG, "🔄 处理type=" + site.getType() + "站点");
                Response response = OkHttp.newCall(site.getApi(), site.getHeaders()).execute();
                String homeContent = response.body().string();
                Log.d(TAG, "📊 type=" + site.getType() + "结果长度: " + (homeContent != null ? homeContent.length() : 0));
                SpiderDebug.log(homeContent);
                response.close();
                return fetchPic(site, Result.fromType(site.getType(), homeContent));
            }
        });
    }

    public void categoryContent(String key, String tid, String page, boolean filter, HashMap<String, String> extend) {
        // ✅ 记录分类状态
        this.lastTypeId = tid;
        this.lastPage = Integer.parseInt(page);

        execute(result, () -> {
            Site site = VodConfig.get().getSite(key);
            if (site.getType() == 3) {
                Spider spider = site.recent().spider();
                String categoryContent = spider.categoryContent(tid, page, filter, extend);
                SpiderDebug.log(categoryContent);
                return Result.fromJson(categoryContent);
            } else {
                ArrayMap<String, String> params = new ArrayMap<>();
                if (site.getType() == 1 && !extend.isEmpty())
                    params.put("f", App.gson().toJson(extend));
                if (site.getType() == 4)
                    params.put("ext", Util.base64(App.gson().toJson(extend), Util.URL_SAFE));
                params.put("ac", site.getType() == 0 ? "videolist" : "detail");
                params.put("t", tid);
                params.put("pg", page);
                String categoryContent = call(site, params);
                SpiderDebug.log(categoryContent);
                return Result.fromType(site.getType(), categoryContent);
            }
        });
    }

    public void detailContent(String key, String id) {
        // 🔥 关键修复：设置详情内容标识，避免与分类内容冲突
        lastTypeId = null; // 清空分类ID，标识这是详情内容
        isDetailContent = true; // 设置详情内容标识
        currentSiteKey = key; // 🔑 关键修复：保存当前站点Key

        android.util.Log.d("ONETV_SITE_VM", "🎬 [FongMi_TV兼容] detailContent调用: key=" + key + ", id=" + id);
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] SiteViewModel.detailContent开始执行");
        android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置currentSiteKey: " + currentSiteKey);

        execute(result, () -> {
            android.util.Log.d("ONETV_SITE_VM", "🔄 [电影ID跟踪] execute方法开始执行");
            // 参数验证 - 基于原版FongMi_TV的严格验证
            if (id == null || id.isEmpty()) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 无效的电影ID: " + id);
                throw new IllegalArgumentException("电影ID不能为空");
            }

            // 🔥 删除临时ID检查逻辑
            // 原版FongMi_TV中不存在临时ID概念

            if (id.startsWith("msearch:")) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] msearch类型ID无法获取详情: " + id);
                throw new IllegalArgumentException("msearch类型ID需要先进行搜索转换");
            }

            android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ID验证通过: " + id);

            Site site = VodConfig.get().getSite(key);
            if (site == null) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 站点未找到: " + key);
                android.util.Log.e("ONETV_SITE_VM", "🎯 [电影ID跟踪] 站点查找失败，抛出异常");
                throw new IllegalArgumentException("站点未找到: " + key);
            }

            android.util.Log.d("ONETV_SITE_VM", "🔄 [FongMi_TV兼容] 开始获取详情: 站点=" + site.getName() + ", ID=" + id);
            android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 站点验证成功，站点类型: " + site.getType());

            if (site.getType() == 3) {
                // Spider类型站点处理
                android.util.Log.d("ONETV_SITE_VM", "🕷️ [FongMi_TV兼容] Spider站点处理: " + site.getName());
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 开始Spider类型站点处理");

                Spider spider = site.recent().spider();
                if (spider == null) {
                    android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] Spider创建失败");
                    android.util.Log.e("ONETV_SITE_VM", "🎯 [电影ID跟踪] Spider创建失败，抛出异常");
                    throw new RuntimeException("Spider创建失败");
                }

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Spider创建成功，开始调用detailContent");
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 调用Spider.detailContent，参数: " + id);

                String detailContent = spider.detailContent(Arrays.asList(id));
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] Spider.detailContent返回，结果长度: "
                        + (detailContent != null ? detailContent.length() : 0));

                SpiderDebug.log(detailContent);
                Result result = Result.fromJson(detailContent);
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] JSON解析完成，结果列表大小: "
                        + (result != null && result.getList() != null ? result.getList().size() : 0));

                // 🔥 修复：添加海报URL检查日志，诊断海报获取问题
                if (!result.getList().isEmpty()) {
                    Vod vod = result.getList().get(0);
                    String vodPic = vod.getVodPic();
                    if (vodPic == null || vodPic.isEmpty()) {
                        android.util.Log.w("VOD_FLOW",
                                "[SPIDER_PIC_WARNING] ⚠️ Spider返回的电影详情中海报URL为空: " + vod.getVodName());
                        android.util.Log.w("VOD_FLOW", "[SPIDER_PIC_WARNING] ⚠️ 原始JSON长度: "
                                + (detailContent != null ? detailContent.length() : 0));
                        // 检查原始JSON中是否包含pic相关字段
                        if (detailContent != null) {
                            boolean hasPic = detailContent.contains("\"vod_pic\"") || detailContent.contains("\"pic\"");
                            android.util.Log.w("VOD_FLOW", "[SPIDER_PIC_WARNING] ⚠️ 原始JSON是否包含pic字段: " + hasPic);
                        }

                        // 🔥 修复：如果Spider返回的海报为空，尝试通过fetchPic补充获取
                        android.util.Log.d("VOD_FLOW", "[SPIDER_PIC_FETCH] 🔄 尝试通过fetchPic补充获取海报");
                        try {
                            result = fetchPic(site, result);
                            if (!result.getList().isEmpty()) {
                                String newVodPic = result.getList().get(0).getVodPic();
                                if (newVodPic != null && !newVodPic.isEmpty()) {
                                    android.util.Log.d("VOD_FLOW",
                                            "[SPIDER_PIC_FETCH_SUCCESS] ✅ fetchPic成功获取海报: " + newVodPic);
                                } else {
                                    android.util.Log.w("VOD_FLOW", "[SPIDER_PIC_FETCH_FAILED] ⚠️ fetchPic仍未获取到海报");
                                }
                            }
                        } catch (Exception e) {
                            android.util.Log.e("VOD_FLOW", "[SPIDER_PIC_FETCH_ERROR] ❌ fetchPic调用失败: " + e.getMessage(),
                                    e);
                        }
                    } else {
                        android.util.Log.d("VOD_FLOW", "[SPIDER_PIC_SUCCESS] ✅ Spider返回的电影详情包含海报URL: " + vodPic);
                    }
                    vod.setVodFlags();
                }
                if (!result.getList().isEmpty())
                    Source.get().parse(result.getList().get(0).getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Spider详情获取成功");
                return result;
            } else if (site.isEmpty() && "push_agent".equals(key)) {
                // Push代理处理
                android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] Push代理处理: " + id);

                Vod vod = new Vod();
                vod.setVodId(id);
                vod.setVodName(id);
                vod.setVodPic(ResUtil.getString(R.string.vod_push_image));
                vod.setVodFlags(Flag.create(ResUtil.getString(R.string.vod_push), id));
                Source.get().parse(vod.getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Push代理处理成功");
                return Result.vod(vod);
            } else {
                // API类型站点处理
                android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] API站点处理: " + site.getName());

                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("ac", site.getType() == 0 ? "videolist" : "detail");
                params.put("ids", id);
                String detailContent = call(site, params);
                SpiderDebug.log(detailContent);
                Result result = Result.fromType(site.getType(), detailContent);

                // 🔥 修复：添加海报URL检查日志，诊断API站点海报获取问题
                if (!result.getList().isEmpty()) {
                    Vod vod = result.getList().get(0);
                    String vodPic = vod.getVodPic();
                    if (vodPic == null || vodPic.isEmpty()) {
                        android.util.Log.w("VOD_FLOW",
                                "[API_PIC_WARNING] ⚠️ API站点返回的电影详情中海报URL为空: " + vod.getVodName());
                        android.util.Log.w("VOD_FLOW", "[API_PIC_WARNING] ⚠️ 站点类型: " + site.getType() + ", 原始数据长度: "
                                + (detailContent != null ? detailContent.length() : 0));
                        // 检查原始数据中是否包含pic相关字段
                        if (detailContent != null) {
                            boolean hasPic = detailContent.contains("\"vod_pic\"") || detailContent.contains("\"pic\"")
                                    || detailContent.contains("<pic>");
                            android.util.Log.w("VOD_FLOW", "[API_PIC_WARNING] ⚠️ 原始数据是否包含pic字段: " + hasPic);
                        }

                        // 🔥 修复：如果API返回的海报为空，尝试通过fetchPic补充获取
                        android.util.Log.d("VOD_FLOW", "[API_PIC_FETCH] 🔄 尝试通过fetchPic补充获取海报");
                        try {
                            result = fetchPic(site, result);
                            if (!result.getList().isEmpty()) {
                                String newVodPic = result.getList().get(0).getVodPic();
                                if (newVodPic != null && !newVodPic.isEmpty()) {
                                    android.util.Log.d("VOD_FLOW",
                                            "[API_PIC_FETCH_SUCCESS] ✅ fetchPic成功获取海报: " + newVodPic);
                                } else {
                                    android.util.Log.w("VOD_FLOW", "[API_PIC_FETCH_FAILED] ⚠️ fetchPic仍未获取到海报");
                                }
                            }
                        } catch (Exception e) {
                            android.util.Log.e("VOD_FLOW", "[API_PIC_FETCH_ERROR] ❌ fetchPic调用失败: " + e.getMessage(),
                                    e);
                        }
                    } else {
                        android.util.Log.d("VOD_FLOW", "[API_PIC_SUCCESS] ✅ API站点返回的电影详情包含海报URL: " + vodPic);
                    }
                    vod.setVodFlags();
                }
                if (!result.getList().isEmpty())
                    Source.get().parse(result.getList().get(0).getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] API详情获取成功");
                return result;
            }
        });
    }

    public void playerContent(String key, String flag, String id) {
        // 🔥 修复：设置播放器操作状态，防止并发homeContent调用干扰
        isPlayerOperationActive = true;
        android.util.Log.d("VOD_FLOW", "🔒 [并发控制] 设置播放器操作状态为活跃，防止homeContent干扰");

        // 🔥 获取当前FlowID用于完整的日志跟踪 - 声明为final
        final String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId() != null
                ? VodFlowTracker.INSTANCE.getCurrentFlowId()
                : "UNKNOWN";

        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + currentFlowId + "] [PLAYER_CONTENT_ENTRY] SiteViewModel.playerContent方法被调用");
        android.util.Log.d("-VOD_FLOW",
                "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                        + currentFlowId + "] [PLAYER_CONTENT_ENTRY] 参数: key=" + key + ", flag=" + flag + ", id=" + id);

        execute(player, () -> {
            try {
                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [PLAYER_CONTENT_CALLABLE] playerContent的callable开始执行");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [PLAYER_CONTENT_CALLABLE] 播放地址解析任务开始");

                // 🔥 关键修复：必须使用上下文中的FlowID，确保FlowID连续性
                String flowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
                if (flowId == null) {
                    // 🔥 如果没有FlowID，使用传入的currentFlowId
                    flowId = currentFlowId;
                    VodFlowTracker.INSTANCE.setCurrentFlowId(flowId);
                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_FLOWID] 使用传入的FlowID");
                } else {
                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_FLOWID] 使用现有FlowID");
                }
                long startTime = System.currentTimeMillis();

                VodFlowTracker.INSTANCE.logPlayerContentStart(flowId, key, flag, id);
                android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_START] 开始解析播放地址");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址解析开始 key:" + key + " flag:" + flag
                                + " id:" + id);

                Source.get().stop();
                Site site = VodConfig.get().getSite(key);
                android.util.Log.d(TAG,
                        "[FlowID:" + flowId + "] [PLAYER_CONTENT_SITE] 站点信息: "
                                + (site != null ? site.getName() : "null")
                                + ", type=" + (site != null ? site.getType() : "null"));

                if (site.getType() == 3) {
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_JS] JavaScript Spider获取播放地址");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取类型 [" + key
                                    + "] JavaScript Spider");
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取类型 [" + key
                                    + "] JavaScript Spider");

                    Spider spider = site.recent().spider();
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [SPIDER_CREATE] 创建Spider [" + key + "] API: "
                                    + site.getApi());
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [SPIDER_TYPE_CHECK] 站点 [" + key + "] API: "
                                    + site.getApi() + ", JS: false, PY: false, CSP: true");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 调用Spider方法 [" + key
                                    + "] playerContent(flag:" + flag + ", id:" + id + ")");
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 调用Spider方法 [" + key
                                    + "] playerContent(flag:" + flag + ", id:" + id + ")");

                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 开始获取播放地址 [" + key + "] flag:" + flag
                                    + " id:" + id);
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 开始获取播放地址 [" + key + "] flag:" + flag
                                    + " id:" + id);

                    String playerContent = spider.playerContent(flag, id, VodConfig.get().getFlags());
                    long responseTime = System.currentTimeMillis();
                    int dataLength = playerContent != null ? playerContent.length() : 0;
                    long duration = responseTime - startTime;

                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 获取播放地址成功 [" + key + "] flag:" + flag
                                    + " id:" + id + "，返回数据长度: " + dataLength);
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 获取播放地址成功 [" + key + "] flag:" + flag
                                    + " id:" + id + "，返回数据长度: " + dataLength);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取完成 [" + key + "] flag:" + flag
                                    + " id:" + id + "，耗时: " + duration + "ms");
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取完成 [" + key + "] flag:" + flag
                                    + " id:" + id + "，耗时: " + duration + "ms");
                    android.util.Log.d(TAG,
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_RESPONSE] Spider返回数据长度: " + dataLength);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] Spider响应 [" + key + "] flag:" + flag
                                    + " id:" + id + "，数据长度: " + dataLength);
                    android.util.Log.d(TAG,
                            "[" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] Spider响应 [" + key + "] flag:" + flag
                                    + " id:" + id + "，数据长度: " + dataLength);
                    SpiderDebug.log(playerContent);

                    Result result = Result.fromJson(playerContent);
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_JSON] JSON解析完成: "
                            + (result != null ? "成功" : "失败"));

                    if (result.getFlag().isEmpty())
                        result.setFlag(flag);

                    // 🔥 关键：Source.fetch()解析特殊前缀地址
                    String urlBefore = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_BEFORE] 解析前URL: " + urlBefore);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] Source.fetch()解析前: " + urlBefore);

                    result.setUrl(Source.get().fetch(result));

                    String urlAfter = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_AFTER] 解析后URL: " + urlAfter);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] Source.fetch()解析后: " + urlAfter);

                    long completeTime = System.currentTimeMillis();
                    boolean needParse = result.getParse() == 1;
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成(Spider): URL=" + urlAfter
                                    + ", 需要解析=" + needParse + ", 耗时=" + (completeTime - startTime) + "ms");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] Spider解析完成，最终URL: " + urlAfter);

                    result.setHeader(site.getHeader());
                    result.setKey(key);

                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_RETURN] 即将返回Result对象");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 返回Result对象给execute方法");
                    return result;
                } else if (site.getType() == 4) {
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_API] 使用API解析 (type=4)");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取类型 [" + key + "] API (type=4)");

                    ArrayMap<String, String> params = new ArrayMap<>();
                    params.put("play", id);
                    params.put("flag", flag);
                    String playerContent = call(site, params);
                    long responseTime = System.currentTimeMillis();
                    int dataLength = playerContent != null ? playerContent.length() : 0;

                    android.util.Log.d(TAG,
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_RESPONSE] API调用返回数据长度: " + dataLength);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] API响应 [" + key + "] flag:" + flag
                                    + " id:" + id + "，数据长度: " + dataLength);
                    SpiderDebug.log(playerContent);

                    Result result = Result.fromJson(playerContent);
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_JSON] JSON解析完成: "
                            + (result != null ? "成功" : "失败"));

                    if (result.getFlag().isEmpty())
                        result.setFlag(flag);

                    // 🔥 关键：Source.fetch()解析特殊前缀地址
                    String urlBefore = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_BEFORE] 解析前URL: " + urlBefore);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] API解析前: " + urlBefore);

                    result.setUrl(Source.get().fetch(result));

                    String urlAfter = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_AFTER] 解析后URL: " + urlAfter);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] API解析后: " + urlAfter);

                    long completeTime = System.currentTimeMillis();
                    boolean needParse = result.getParse() == 1;
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成(API): URL=" + urlAfter
                                    + ", 需要解析=" + needParse + ", 耗时=" + (completeTime - startTime) + "ms");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] API解析完成，最终URL: " + urlAfter);

                    result.setHeader(site.getHeader());

                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_RETURN] 即将返回Result对象");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 返回Result对象给execute方法");
                    return result;
                } else if (site.isEmpty() && "push_agent".equals(key)) {
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_PUSH] 使用推送代理解析 (push_agent)");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取类型 [" + key + "] 推送代理解析");

                    Result result = new Result();
                    result.setParse(0);
                    result.setFlag(flag);
                    result.setUrl(Url.create().add(id));

                    // 🔥 关键：Source.fetch()解析特殊前缀地址
                    String urlBefore = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_BEFORE] 解析前URL: " + urlBefore);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 推送代理解析前: " + urlBefore);

                    result.setUrl(Source.get().fetch(result));

                    String urlAfter = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_AFTER] 解析后URL: " + urlAfter);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 推送代理解析后: " + urlAfter);

                    long completeTime = System.currentTimeMillis();
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成(推送代理): URL=" + urlAfter
                                    + ", 耗时=" + (completeTime - startTime) + "ms");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 推送代理解析完成，最终URL: " + urlAfter);

                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_RETURN] 即将返回Result对象");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 返回Result对象给execute方法");
                    return result;
                } else {
                    android.util.Log.d(TAG, "[FlowID:" + flowId + "] [PLAYER_CONTENT_DEFAULT] 使用默认解析");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 播放地址获取类型 [" + key + "] 默认解析");

                    Url url = Url.create().add(id);
                    Result result = new Result();
                    result.setUrl(url);
                    result.setFlag(flag);
                    result.setHeader(site.getHeader());
                    result.setPlayUrl(site.getPlayUrl());
                    result.setParse(Sniffer.isVideoFormat(url.v()) && result.getPlayUrl().isEmpty() ? 0 : 1);

                    // 🔥 关键：Source.fetch()解析特殊前缀地址
                    String urlBefore = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_BEFORE] 解析前URL: " + urlBefore);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 默认解析前: " + urlBefore);

                    result.setUrl(Source.get().fetch(result));

                    String urlAfter = result.getUrl() != null ? result.getUrl().v() : "null";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_URL_AFTER] 解析后URL: " + urlAfter);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 默认解析后: " + urlAfter);

                    long completeTime = System.currentTimeMillis();
                    boolean needParse = result.getParse() == 1;
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + flowId + "] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成(默认): URL=" + urlAfter
                                    + ", 需要解析=" + needParse + ", 耗时=" + (completeTime - startTime) + "ms");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 默认解析完成，最终URL: " + urlAfter);

                    SpiderDebug.log(result.toString());

                    android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [PLAYER_CONTENT_RETURN] 即将返回Result对象");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + flowId + "] [PLAYER_CONTENT] 返回Result对象给execute方法");
                    return result;
                }
            } catch (Exception e) {
                android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId
                        + "] [PLAYER_CONTENT_EXCEPTION] playerContent callable异常: " + e.getMessage(), e);
                android.util.Log.e("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [PLAYER_CONTENT_EXCEPTION] 播放地址解析异常");
                throw e; // 重新抛出异常让execute方法处理
            }
        });
    }

    public void action(String key, String action) {
        execute(this.action, () -> {
            Site site = VodConfig.get().getSite(key);
            if (site.getType() == 3)
                return Result.fromJson(site.recent().spider().action(action));
            if (site.getType() == 4)
                return Result.fromJson(OkHttp.string(action));
            return Result.empty();
        });
    }

    public void searchContent(Site site, String keyword, boolean quick) throws Throwable {
        // 🔥 调用带FlowID的重载方法，使用当前FlowID
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        searchContent(site, keyword, quick, currentFlowId);
    }

    /**
     * 🔥 关键修复：带FlowID参数的搜索方法，确保FlowID连续性
     */
    public void searchContent(Site site, String keyword, boolean quick, String flowId) throws Throwable {
        // 🔥 关键修复：在搜索开始时设置FlowID上下文
        if (flowId != null && !flowId.equals("UNKNOWN")) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.setCurrentFlowId(flowId);
        }

        if (site.getType() == 3) {
            if (quick && !site.isQuickSearch())
                return;

            // 🔥 关键调试：记录搜索参数
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SEARCH_CONTENT_START] 站点: " + site.getName() +
                    ", 原始关键词: '" + keyword + "'" +
                    ", 转换后关键词: '" + Trans.t2s(keyword) + "'" +
                    ", quick: " + quick);

            // 🔥 获取Spider实例并验证
            Spider spider = site.spider();
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SPIDER_VERIFY] 站点: " + site.getName() +
                    ", Spider类型: " + (spider != null ? spider.getClass().getSimpleName() : "null") +
                    ", API: " + site.getApi() +
                    ", JAR: " + site.getJar());

            if (spider == null) {
                android.util.Log.e("VOD_FLOW", "[FlowID:" + flowId + "] [SPIDER_ERROR] 站点Spider为空: " + site.getName());
                return;
            }

            // 🔥 修复关键问题：使用2参数版本的searchContent，与原版FongMi_TV一致
            String searchKeyword = Trans.t2s(keyword);
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SPIDER_SEARCH_CALL] 即将调用Spider.searchContent: " +
                    "keyword='" + searchKeyword + "', quick=" + quick);

            String searchContent = spider.searchContent(searchKeyword, quick);
            SpiderDebug.log(site.getName() + "," + searchContent);

            // 🔥 关键调试：记录原始搜索内容
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SEARCH_RESULT] 站点: " + site.getName() +
                    ", 原始搜索内容长度: " + (searchContent != null ? searchContent.length() : 0));

            Result result = Result.fromJson(searchContent);
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [JSON_PARSE] 站点: " + site.getName() +
                    ", Result: " + (result != null ? "非空" : "空") +
                    ", List大小: " + (result != null && result.getList() != null ? result.getList().size() : 0));

            post(site, result, flowId);
        } else {
            if (quick && !site.isQuickSearch())
                return;
            ArrayMap<String, String> params = new ArrayMap<>();
            params.put("wd", Trans.t2s(keyword));
            params.put("quick", String.valueOf(quick));
            String searchContent = call(site, params);
            SpiderDebug.log(site.getName() + "," + searchContent);
            post(site, fetchPic(site, Result.fromType(site.getType(), searchContent)), flowId);
        }
    }

    public void searchContent(Site site, String keyword, String page) {
        // ✅ 记录搜索状态
        this.lastSearchKeyword = keyword;

        execute(search, () -> {
            if (site.getType() == 3) {
                String searchContent = site.spider().searchContent(Trans.t2s(keyword), false, page);
                SpiderDebug.log(site.getName() + "," + searchContent);
                Result result = Result.fromJson(searchContent);
                for (Vod vod : result.getList())
                    vod.setSite(site);
                return result;
            } else {
                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("wd", Trans.t2s(keyword));
                params.put("pg", page);
                String searchContent = call(site, params);
                SpiderDebug.log(site.getName() + "," + searchContent);
                Result result = fetchPic(site, Result.fromType(site.getType(), searchContent));
                for (Vod vod : result.getList())
                    vod.setSite(site);
                return result;
            }
        });
    }

    private String call(Site site, ArrayMap<String, String> params) throws IOException {
        if (!site.getExt().isEmpty())
            params.put("extend", site.getExt());
        Call get = OkHttp.newCall(site.getApi(), site.getHeaders(), params);
        Call post = OkHttp.newCall(site.getApi(), site.getHeaders(), OkHttp.toBody(params));
        Response response = (site.getExt().length() <= 1000 ? get : post).execute();
        String result = response.body().string();
        response.close();
        return result;
    }

    private Result fetchPic(Site site, Result result) throws Exception {
        if (site.getType() > 2 || result.getList().isEmpty() || !result.getList().get(0).getVodPic().isEmpty())
            return result;
        ArrayList<String> ids = new ArrayList<>();
        if (site.getCategories().isEmpty())
            for (Vod item : result.getList())
                ids.add(item.getVodId());
        else
            for (Vod item : result.getList())
                if (site.getCategories().contains(item.getTypeName()))
                    ids.add(item.getVodId());
        if (ids.isEmpty())
            return result.clear();
        ArrayMap<String, String> params = new ArrayMap<>();
        params.put("ac", site.getType() == 0 ? "videolist" : "detail");
        params.put("ids", TextUtils.join(",", ids));
        Response response = OkHttp.newCall(site.getApi(), site.getHeaders(), params).execute();
        result.setList(Result.fromType(site.getType(), response.body().string()).getList());
        response.close();
        return result;
    }

    private void post(Site site, Result result) {
        // 🔥 调用带FlowID的重载方法，使用当前FlowID
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        post(site, result, currentFlowId);
    }

    /**
     * 🔥 关键修复：带FlowID参数的post方法，确保FlowID连续性
     */
    private void post(Site site, Result result, String flowId) {
        // 🔥 关键修复：在post方法中设置FlowID上下文
        if (flowId != null && !flowId.equals("UNKNOWN")) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.setCurrentFlowId(flowId);
        }

        // 🔥 关键调试：详细记录搜索结果解析情况
        android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [POST_SEARCH_RESULT] 站点搜索结果解析: " + site.getName());
        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + flowId + "] [POST_RESULT_CHECK] Result对象: " + (result != null ? "非空" : "空"));
        if (result != null) {
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + flowId + "] [POST_RESULT_SIZE] List大小: " + result.getList().size());
        }

        if (result.getList().isEmpty()) {
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [POST_NO_RESULT] 站点搜索无结果: " + site.getName());
            return;
        }

        // 设置站点信息
        for (Vod vod : result.getList())
            vod.setSite(site);

        // 原版LiveData通知
        this.search.postValue(result);

        // 🔥 关键：发送EventBus事件，对应原版FongMi_TV的搜索结果处理
        android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [POST_SEARCH_SUCCESS] 站点搜索成功: " + site.getName()
                + ", 结果数: " + result.getList().size());

        // 🔥 原版FongMi_TV：发送增强的搜索结果事件
        String currentFlowId = flowId != null ? flowId : "UNKNOWN";

        // 🔥 原版FongMi_TV：集成SearchStateManager
        top.cywin.onetv.movie.utils.SearchStateManager searchStateManager = top.cywin.onetv.movie.utils.SearchStateManager
                .getInstance();
        String sessionId = searchStateManager.getCurrentSearchSessionId();
        long searchStartTime = searchStateManager.getSearchStartTime();
        long searchDuration = System.currentTimeMillis() - searchStartTime;

        try {
            org.greenrobot.eventbus.EventBus.getDefault().post(
                    new top.cywin.onetv.movie.event.SearchResultEvent(
                            result.getList(), // results: List<Vod>
                            lastSearchKeyword != null ? lastSearchKeyword : "", // keyword: String
                            false, // hasMore: Boolean
                            1, // page: Int
                            result.getList().size(), // total: Int
                            site.getKey(), // siteKey: String
                            site.getName(), // siteName: String
                            sessionId, // sessionId: String
                            searchDuration, // searchDuration: Long
                            false, // isNewSearch: Boolean
                            System.currentTimeMillis() // timestamp: Long
                    ));

            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SITE_VM_POST_SEARCH_EVENT",
                    "SearchResultEvent已发送: " + site.getName() + ", 结果数: " + result.getList().size());
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [SITE_VM_POST_SEARCH_EVENT] SearchResultEvent已发送: " + site.getName()
                            + ", 结果数: " + result.getList().size());
        } catch (Exception e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "SITE_VM_POST_SEARCH_EVENT_ERROR",
                    "发送SearchResultEvent失败: " + e.getMessage());
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [SITE_VM_POST_SEARCH_EVENT_ERROR] 发送SearchResultEvent失败", e);
        }
    }

    private synchronized void execute(MutableLiveData<Result> resultLiveData, Callable<Result> callable) {
        // 🔥 获取当前FlowID用于完整的日志跟踪 - 声明为final
        final String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId() != null
                ? VodFlowTracker.INSTANCE.getCurrentFlowId()
                : "UNKNOWN";

        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_START] SiteViewModel.execute方法被调用");
        android.util.Log.d("-VOD_FLOW", "│ ["
                + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                + currentFlowId + "] [SITE_EXECUTE] 执行器类型: " + (resultLiveData == this.result ? "result"
                        : resultLiveData == this.player ? "player" : resultLiveData == this.search ? "search" : "其他"));

        // 🔥 原版FongMi_TV设计：每次都重新创建2线程池，避免线程积累
        if (executor != null) {
            executor.shutdownNow(); // 🔥 关键：每次都关闭旧线程池
            android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_SHUTDOWN] 关闭旧线程池");
        }

        // 🔥 原版设计：固定2个线程，专门处理内容加载任务
        executor = Executors.newFixedThreadPool(2);
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_CREATE] 创建新的2线程池（原版设计）");
        // 🔥 修复：使用统一的共享线程池
        executor.execute(() -> {
            try {
                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_CALLABLE_START] 开始执行callable任务");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [SITE_CALLABLE] 异步任务开始执行");

                // 🔥 修复：更安全的中断处理，避免任务失败
                Result resultData;
                try {
                    // 🔥 修复：检查线程中断状态，如果被中断则返回空结果，避免异常
                    if (Thread.currentThread().isInterrupted()) {
                        android.util.Log.w("VOD_FLOW",
                                "[FlowID:" + currentFlowId + "] [SITE_CALLABLE_INTERRUPTED] 线程已被中断，返回空结果");
                        resultData = Result.empty();
                    } else {
                        resultData = callable.call();
                    }
                } catch (InterruptedException e) {
                    android.util.Log.w("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [SITE_CALLABLE_INTERRUPTED] 任务执行时被中断，返回空结果");
                    // 🔥 修复：不重新设置中断状态，避免影响其他任务
                    resultData = Result.empty();
                } catch (Exception e) {
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [SITE_CALLABLE_ERROR] 任务执行异常: " + e.getMessage());
                    resultData = Result.empty();
                }

                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId
                        + "] [SITE_CALLABLE_COMPLETE] callable任务执行完成，结果: " + (resultData != null ? "非空" : "空"));
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [SITE_CALLABLE] 异步任务执行完成");

                resultLiveData.postValue(resultData);

                // ✅ 添加EventBus通知机制
                android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_NOTIFY_START] 准备通知Compose UI");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [SITE_NOTIFY] 通知UI组件处理结果");

                // 🔥 关键修复：使用正确的参数名，避免变量名冲突
                notifyComposeUI(resultLiveData, resultData);

                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [SITE_NOTIFY_COMPLETE] notifyComposeUI调用完成");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [SITE_NOTIFY] UI通知完成");

            } catch (Throwable e) {
                // 🔥 修复：提供更详细的异常信息
                String errorMsg = e.getMessage();
                if (errorMsg == null || errorMsg.isEmpty()) {
                    errorMsg = e.getClass().getSimpleName() + ": " + e.toString();
                }

                android.util.Log.e("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_ERROR] execute方法执行异常: " + errorMsg);
                android.util.Log.e("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [FlowID:" + currentFlowId + "] [SITE_EXECUTE_ERROR] 异步任务执行失败");
                android.util.Log.e("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_ERROR_DETAIL] 异常类型: " + e.getClass().getName());
                android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_EXECUTE_ERROR_STACK] 异常堆栈: ", e);

                if (e instanceof InterruptedException || Thread.interrupted())
                    return;

                Result errorResult;
                if (e.getCause() instanceof ExtractException) {
                    String extractMsg = e.getCause().getMessage();
                    if (extractMsg == null)
                        extractMsg = "提取异常";
                    errorResult = Result.error(extractMsg);
                    // ✅ 通知Compose UI错误
                    EventBus.getDefault().post(new ErrorEvent(extractMsg, ErrorEvent.Type.EXTRACT));
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [SITE_ERROR_EXTRACT] 提取异常: " + extractMsg);
                } else {
                    errorResult = Result.empty();
                    // ✅ 通知Compose UI错误
                    EventBus.getDefault().post(new ErrorEvent("请求失败: " + errorMsg, ErrorEvent.Type.NETWORK));
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [SITE_ERROR_NETWORK] 网络异常: " + errorMsg);
                }
                resultLiveData.postValue(errorResult);
            } finally {
                // 🔥 修复：播放器操作完成后重置状态标志，允许后续homeContent调用
                if (resultLiveData == this.player) {
                    isPlayerOperationActive = false;
                    android.util.Log.d("VOD_FLOW", "🔓 [并发控制] 播放器操作完成，重置状态标志，允许homeContent调用");
                }
            }
        });
    }

    /**
     * 通知Compose UI - 根据结果类型发送相应事件
     */
    private void notifyComposeUI(MutableLiveData<Result> resultLiveData, Result resultData) {
        // 🔥 获取当前FlowID用于完整的日志跟踪 - 优先使用全局FlowID
        String currentFlowId = VodFlowTracker.INSTANCE.getGlobalFlowId();
        if (currentFlowId == null) {
            currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        }
        if (currentFlowId == null) {
            currentFlowId = "UNKNOWN";
        }

        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_NOTIFY_UI_START] notifyComposeUI被调用");
        android.util.Log.d("-VOD_FLOW",
                "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                        + currentFlowId + "] [SITE_NOTIFY_UI] resultLiveData类型: "
                        + (resultLiveData == result ? "result"
                                : resultLiveData == search ? "search" : resultLiveData == player ? "player" : "其他"));
        android.util.Log.d("-VOD_FLOW",
                "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                        + currentFlowId + "] [SITE_NOTIFY_UI] isDetailContent标志: " + isDetailContent);
        try {
            // ✅ 根据LiveData类型判断操作类型
            if (resultLiveData == search) {
                // 搜索结果
                EventBus.getDefault().post(new SearchResultEvent(
                        resultData.getList(),
                        lastSearchKeyword,
                        resultData.getList().size() >= 20,
                        lastPage,
                        resultData.getTotal(),
                        "",
                        "",
                        "",
                        0L,
                        false,
                        System.currentTimeMillis()));
            } else if (resultLiveData == result) {
                // 🔥 关键修复：根据isDetailContent标识区分详情内容和分类内容
                if (isDetailContent) {
                    // 详情内容
                    Vod vod = resultData.getList().isEmpty() ? null : resultData.getList().get(0);

                    android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] 准备发送ContentDetailEvent");
                    android.util.Log.d("ONETV_SITE_VM",
                            "📊 [FongMi_TV兼容] 详情数据: vod=" + (vod != null ? vod.getVodName() : "null"));
                    android.util.Log.d("ONETV_SITE_VM", "🔍 [FongMi_TV兼容] LiveData类型: result (详情内容)");

                    // 🔥 关键修复：确保Vod对象包含正确的siteKey
                    if (vod != null && currentSiteKey != null) {
                        Site site = VodConfig.get().getSite(currentSiteKey);
                        if (site != null) {
                            vod.setSite(site);
                            android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置Vod的Site: " + currentSiteKey);
                            android.util.Log.d("ONETV_SITE_VM",
                                    "🎯 [电影ID跟踪] Vod详情: vodId=" + vod.getVodId() + ", siteKey="
                                            + vod.getSiteKey() + ", name=" + vod.getVodName());
                        } else {
                            android.util.Log.w("ONETV_SITE_VM", "⚠️ [架构修复] 未找到站点: " + currentSiteKey);
                        }
                    }

                    EventBus.getDefault().post(new ContentDetailEvent(vod, true, null));

                    android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ContentDetailEvent已发送");

                    // 重置标识
                    isDetailContent = false;
                } else if (!TextUtils.isEmpty(lastTypeId)) {
                    // 分类内容
                    EventBus.getDefault().post(new CategoryContentEvent(
                            resultData.getList(),
                            lastTypeId,
                            lastPage,
                            resultData.getList().size() >= 20,
                            resultData.getTotal()));
                } else {
                    // 首页内容
                    EventBus.getDefault().post(new HomeContentEvent(
                            resultData.getTypes(),
                            resultData.getList(),
                            true));
                }
            } else if (resultLiveData == action) {
                // action类型的详情内容（如果有的话）
                Vod vod = resultData.getList().isEmpty() ? null : resultData.getList().get(0);

                android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] 准备发送ContentDetailEvent (action类型)");
                android.util.Log.d("ONETV_SITE_VM",
                        "📊 [FongMi_TV兼容] 详情数据: vod=" + (vod != null ? vod.getVodName() : "null"));

                // 🔥 关键修复：确保Vod对象包含正确的siteKey (action类型)
                if (vod != null && currentSiteKey != null) {
                    Site site = VodConfig.get().getSite(currentSiteKey);
                    if (site != null) {
                        vod.setSite(site);
                        android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置Vod的Site (action类型): " + currentSiteKey);
                    } else {
                        android.util.Log.w("ONETV_SITE_VM", "⚠️ [架构修复] 未找到站点 (action类型): " + currentSiteKey);
                    }
                }

                EventBus.getDefault().post(new ContentDetailEvent(vod, true, null));

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ContentDetailEvent已发送 (action类型)");
            } else if (resultLiveData == player) {
                // 🔥 修复：播放地址解析结果处理
                android.util.Log.d(TAG, "[PLAYER_RESULT] 收到播放地址解析结果");
                android.util.Log.d("-VOD_FLOW",
                        "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                + "] [PLAYER_RESULT] 播放地址解析结果处理开始");

                // 播放地址解析的结果是Result对象，不是Vod对象
                if (resultData instanceof Result) {
                    Result playResult = (Result) resultData;
                    // 🔥 关键修复：使用Url.v()方法获取实际的URL字符串，而不是toString()
                    String url = playResult.getUrl() != null ? playResult.getUrl().v() : "null";
                    String flag = playResult.getFlag() != null ? playResult.getFlag() : "null";
                    int parse = playResult.getParse();

                    android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId
                            + "] [PLAYER_RESULT_DATA] Result对象解析: url=" + url + ", flag=" + flag + ", parse=" + parse);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + currentFlowId + "] [PLAYER_RESULT] 播放地址数据: URL=" + url + " FLAG="
                                    + flag + " PARSE=" + parse);

                    // 🔥 关键修复：使用Url.v()方法获取实际的URL字符串
                    String finalPlayUrl = playResult.getUrl() != null ? playResult.getUrl().v() : "";
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [PLAYER_RESULT_FINAL] 最终播放地址: " + finalPlayUrl);

                    // 🔥 第三阶段：播放阶段开始 - 对标原版FongMi_TV
                    android.util.Log.d("VOD_FLOW", "=== [FlowID:" + currentFlowId + "] === 第三阶段：播放阶段开始 ===");
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [PLAYER_RESULT] 播放地址解析结果: " + finalPlayUrl);
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + currentFlowId + "] [PLAYER_RESULT] 播放地址解析完成: " + finalPlayUrl);

                    // 🔥 发送播放地址解析事件
                    EventBus.getDefault().post(
                            new PlayUrlParseEvent(finalPlayUrl, playResult.getHeaders(), "", 0, playResult.getFlag(),
                                    currentFlowId));
                    android.util.Log.d("VOD_FLOW",
                            "[FlowID:" + currentFlowId + "] [PLAYER_RESULT_SUCCESS] PlayUrlParseEvent已发送");
                    android.util.Log.d("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + currentFlowId + "] [PLAYER_RESULT] 播放地址解析完成，事件已发送: "
                                    + finalPlayUrl);
                } else {
                    android.util.Log.e("VOD_FLOW", "[FlowID:" + currentFlowId + "] [PLAYER_RESULT_ERROR] 结果类型错误: "
                            + (resultData != null ? resultData.getClass().getSimpleName() : "null"));
                    android.util.Log.e("-VOD_FLOW",
                            "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date())
                                    + "] [FlowID:" + currentFlowId + "] [PLAYER_RESULT] 播放地址解析失败，结果类型错误");
                }
            }
        } catch (Exception e) {
            // 通知失败不影响主流程
            e.printStackTrace();
        }
    }

    // ✅ 添加便捷方法供Compose UI调用

    /**
     * 🔥 原版FongMi_TV：搜索内容 - 100%移植原版搜索系统
     */
    public void searchContent(String keyword, boolean quick) {
        // 🔥 关键修复：使用现有FlowID，确保搜索流程FlowID连续性
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null || "UNKNOWN".equals(currentFlowId)) {
            // 如果没有FlowID，从全局FlowID获取
            currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getGlobalFlowId();
            if (currentFlowId != null) {
                top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.setCurrentFlowId(currentFlowId);
                android.util.Log.d("VOD_FLOW",
                        "[FlowID:" + currentFlowId + "] [SITE_VM_SEARCH_FLOWID_RESTORE] 从全局FlowID恢复");
            } else {
                currentFlowId = "UNKNOWN";
            }
        }

        // 🔥 修复：发送搜索开始事件
        org.greenrobot.eventbus.EventBus.getDefault().post(
                new top.cywin.onetv.movie.event.SearchStartEvent(keyword, currentFlowId, System.currentTimeMillis()));

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SITE_VM_SEARCH_CONTENT",
                "开始多站点搜索: keyword=" + keyword + ", quick=" + quick);
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_VM_SEARCH_CONTENT] 开始多站点搜索: keyword="
                + keyword + ", quick=" + quick);

        // 🔥 修复：直接开始新搜索，不进行复杂的状态检查
        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SITE_VM_SEARCH_NEW",
                "开始新关键词搜索: " + keyword);
        android.util.Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [SITE_VM_SEARCH_NEW] 开始新关键词搜索: " + keyword);

        // 🔥 修复：在开始搜索前初始化搜索状态管理器
        top.cywin.onetv.movie.utils.SearchStateManager.getInstance().startNewSearch(keyword);

        startNewSearch(keyword, quick, currentFlowId);
    }

    /**
     * 🔥 原版FongMi_TV：开始新搜索
     */
    private void startNewSearch(String keyword, boolean quick, String flowId) {
        this.lastSearchKeyword = keyword;

        // 更新搜索状态管理器
        top.cywin.onetv.movie.utils.SearchStateManager.getInstance().startNewSearch(keyword);

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_START_NEW_SEARCH",
                "启动新搜索会话: " + keyword);
        android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SITE_VM_START_NEW_SEARCH] 启动新搜索会话: " + keyword);

        // 启动多站点搜索
        startMultiSiteSearch(keyword, quick, flowId);
    }

    /**
     * 🔥 原版FongMi_TV：继续现有搜索
     */
    private void continueSearch(String keyword, boolean quick, String flowId) {
        top.cywin.onetv.movie.utils.SearchStateManager searchStateManager = top.cywin.onetv.movie.utils.SearchStateManager
                .getInstance();

        // 获取尚未搜索的站点
        java.util.List<top.cywin.onetv.movie.bean.Site> remainingSites = searchStateManager
                .getRemainingUnSearchedSites();

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_CONTINUE_SEARCH",
                "继续搜索，剩余未搜索站点数: " + remainingSites.size());
        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + flowId + "] [SITE_VM_CONTINUE_SEARCH] 继续搜索，剩余未搜索站点数: " + remainingSites.size());

        if (remainingSites.isEmpty()) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_SEARCH_COMPLETE",
                    "所有站点已搜索完成");
            android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SITE_VM_SEARCH_COMPLETE] 所有站点已搜索完成");
            return;
        }

        // 继续搜索剩余站点
        top.cywin.onetv.movie.utils.PauseExecutor searchExecutor = getSearchExecutor();
        for (top.cywin.onetv.movie.bean.Site site : remainingSites) {
            searchExecutor.execute(() -> {
                try {
                    searchSingleSite(site, keyword, quick, flowId);
                } catch (Throwable e) {
                    top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId,
                            "SITE_VM_CONTINUE_SEARCH_ERROR",
                            "站点搜索失败: " + site.getName() + ", 错误: " + e.getMessage());
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + flowId + "] [SITE_VM_CONTINUE_SEARCH_ERROR] 站点搜索失败: " + site.getName(), e);
                }
            });
        }
    }

    /**
     * 🔥 原版FongMi_TV：启动多站点搜索
     */
    private void startMultiSiteSearch(String keyword, boolean quick, String flowId) {
        // 🔥 关键：完全按照原版FongMi_TV的CollectActivity.setSite()逻辑
        java.util.List<Site> mSites = new java.util.ArrayList<>();
        int totalSites = VodConfig.get().getSites().size();
        int searchableSites = 0;
        int cspSites = 0;
        int jsSites = 0;

        android.util.Log.d("ONETV_SITE_VM", "📊 [站点统计] 总站点数: " + totalSites);

        for (Site site : VodConfig.get().getSites()) {
            // 统计站点类型
            if (site.getApi().startsWith("csp_")) {
                cspSites++;
            } else if (site.getApi().contains(".js")) {
                jsSites++;
            }

            if (site.isSearchable()) {
                mSites.add(site);
                searchableSites++;
                android.util.Log.d("ONETV_SITE_VM",
                        "✅ [可搜索站点] " + site.getName() + " (type=" + site.getType() + ", api=" + site.getApi() + ")");
            } else {
                android.util.Log.d("ONETV_SITE_VM",
                        "❌ [不可搜索站点] " + site.getName() + " (searchable=" + site.getSearchable() + ")");
            }
        }

        android.util.Log.d("ONETV_SITE_VM",
                "📊 [站点统计] CSP站点: " + cspSites + ", JS站点: " + jsSites + ", 可搜索站点: " + searchableSites);

        // 按照原版逻辑：将home站点移到第一位
        Site home = VodConfig.get().getHome();
        if (mSites.contains(home)) {
            mSites.remove(home);
            mSites.add(0, home);
        }

        android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] 获取到 " + mSites.size() + " 个可搜索站点");

        // 🔥 关键调试：检查前5个可搜索站点的详细信息
        android.util.Log.d("ONETV_SITE_VM", "📋 [前5个可搜索站点详情]:");
        for (int i = 0; i < Math.min(5, mSites.size()); i++) {
            Site site = mSites.get(i);
            android.util.Log.d("ONETV_SITE_VM", String.format("  %d. %s (key=%s, type=%d, api=%s, jar=%s)",
                    i + 1, site.getName(), site.getKey(), site.getType(), site.getApi(),
                    site.getJar() != null ? site.getJar().substring(0, Math.min(50, site.getJar().length())) + "..."
                            : "null"));
        }

        // 🔥 原版FongMi_TV设计：使用专门的PauseExecutor进行多站点搜索
        // 获取全局搜索线程池
        top.cywin.onetv.movie.utils.PauseExecutor searchExecutor = getSearchExecutor();

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_MULTI_SITE_SEARCH_START",
                "使用PauseExecutor进行多站点搜索，站点数: " + mSites.size());
        android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId
                + "] [SITE_VM_MULTI_SITE_SEARCH_START] 使用PauseExecutor进行多站点搜索，站点数: " + mSites.size());

        // 🔥 关键：完全按照原版FongMi_TV的搜索逻辑
        for (Site site : mSites) {
            searchExecutor.execute(() -> {
                try {
                    searchSingleSite(site, keyword, quick, flowId);
                } catch (Throwable e) {
                    top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId,
                            "SITE_VM_SINGLE_SITE_SEARCH_ERROR",
                            "站点搜索失败: " + site.getName() + ", 错误: " + e.getMessage());
                    android.util.Log.e("VOD_FLOW",
                            "[FlowID:" + flowId + "] [SITE_VM_SINGLE_SITE_SEARCH_ERROR] 站点搜索失败: " + site.getName(), e);
                }
            });
        }

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_MULTI_SITE_SEARCH_SUBMITTED",
                "所有搜索任务已提交到PauseExecutor");
        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + flowId + "] [SITE_VM_MULTI_SITE_SEARCH_SUBMITTED] 所有搜索任务已提交到PauseExecutor");
    }

    /**
     * 🔥 原版FongMi_TV：单站点搜索
     */
    private void searchSingleSite(Site site, String keyword, boolean quick, String flowId) throws Throwable {
        // 🔥 关键修复：在子线程中设置FlowID上下文，确保FlowID连续性
        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.setCurrentFlowId(flowId);

        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_SINGLE_SITE_SEARCH_START",
                "开始搜索站点: " + site.getName() + " (" + site.getKey() + ")");
        android.util.Log.d("VOD_FLOW", "[FlowID:" + flowId + "] [SITE_VM_SINGLE_SITE_SEARCH_START] 开始搜索站点: "
                + site.getName() + " (" + site.getKey() + ")");

        try {
            // 🔥 关键修复：调用带FlowID的搜索方法，确保FlowID传递
            searchContent(site, keyword, quick, flowId);

            // 标记站点搜索完成
            top.cywin.onetv.movie.utils.SearchStateManager.getInstance().markSiteSearched(site.getKey());

            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId,
                    "SITE_VM_SINGLE_SITE_SEARCH_SUCCESS",
                    "站点搜索完成: " + site.getName());
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + flowId + "] [SITE_VM_SINGLE_SITE_SEARCH_SUCCESS] 站点搜索完成: " + site.getName());

        } catch (Throwable e) {
            // 即使失败也要标记为已搜索，避免重复尝试
            top.cywin.onetv.movie.utils.SearchStateManager.getInstance().markSiteSearched(site.getKey());

            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(flowId, "SITE_VM_SINGLE_SITE_SEARCH_FAILED",
                    "站点搜索失败: " + site.getName() + ", 错误: " + e.getMessage());
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + flowId + "] [SITE_VM_SINGLE_SITE_SEARCH_FAILED] 站点搜索失败: " + site.getName(), e);

            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 分类内容 - Compose UI调用入口
     */
    public void categoryContent(String typeId, int page, boolean more, java.util.Map<String, String> filters) {
        this.lastTypeId = typeId;
        this.lastPage = page;

        Site site = VodConfig.get().getHome();
        if (site != null) {
            HashMap<String, String> extend = new HashMap<>();
            if (filters != null)
                extend.putAll(filters);
            categoryContent(site.getKey(), typeId, String.valueOf(page), true, extend);
        }
    }

    /**
     * 获取内容详情 - Compose UI调用入口
     */
    public void detailContent(String vodId) {
        android.util.Log.d("ONETV_SITE_VM", "🎬 [FongMi_TV兼容] detailContent调用: vodId=" + vodId);
        Site site = VodConfig.get().getHome();
        if (site != null) {
            android.util.Log.d("ONETV_SITE_VM", "🔑 [FongMi_TV兼容] 使用站点: " + site.getName() + ", key: " + site.getKey());
            detailContent(site.getKey(), vodId);
        } else {
            android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 没有可用的站点");
        }
    }

    /**
     * 搜索转换ID - 实现原版VideoActivity的ID转换机制
     */
    public void searchForIdTransformation(String movieName, String siteKey) {
        android.util.Log.d("ONETV_SITE_VM", "🔄 [FongMi_TV兼容] 开始搜索转换ID: " + movieName + ", siteKey: " + siteKey);

        execute(search, () -> {
            android.util.Log.d("ONETV_SITE_VM", "🔍 [FongMi_TV兼容] 执行搜索以获取真实ID: " + movieName);

            Site site = VodConfig.get().getHome();
            if (site == null) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 没有可用的站点进行搜索");
                EventBus.getDefault()
                        .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, "没有可用的站点"));
                return null;
            }

            try {
                String searchResult;
                if (site.getType() == 3) {
                    // Spider搜索
                    android.util.Log.d("ONETV_SITE_VM", "🕷️ [FongMi_TV兼容] 使用Spider搜索: " + site.getKey());
                    Spider spider = site.recent().spider();
                    searchResult = spider.searchContent(movieName, false);
                } else {
                    // API搜索
                    android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] 使用API搜索: " + site.getKey());
                    ArrayMap<String, String> params = new ArrayMap<>();
                    params.put("ac", site.getType() == 0 ? "videolist" : "list");
                    params.put("wd", movieName);
                    searchResult = call(site, params);
                }

                android.util.Log.d("ONETV_SITE_VM",
                        "📊 [FongMi_TV兼容] 搜索结果长度: " + (searchResult != null ? searchResult.length() : 0));

                Result result = site.getType() == 3 ? Result.fromJson(searchResult)
                        : Result.fromType(site.getType(), searchResult);

                if (result != null && !result.getList().isEmpty()) {
                    // 找到搜索结果，使用第一个结果的vodId
                    Vod firstResult = result.getList().get(0);
                    String realVodId = firstResult.getVodId();
                    String realSiteKey = site.getKey();

                    android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ID转换成功: " + movieName + " -> vodId: "
                            + realVodId + ", siteKey: " + realSiteKey);

                    // 发送ID转换成功事件
                    EventBus.getDefault()
                            .post(new MovieIdTransformEvent("msearch:home", realVodId, realSiteKey, movieName));
                } else {
                    android.util.Log.w("ONETV_SITE_VM", "⚠️ [FongMi_TV兼容] 搜索无结果，无法进行ID转换: " + movieName);
                    EventBus.getDefault()
                            .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, "搜索无结果"));
                }

                return result;
            } catch (Exception e) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] ID转换过程中发生异常: " + movieName, e);
                EventBus.getDefault()
                        .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, e.getMessage()));
                return null;
            }
        });
    }

    /**
     * 解析播放地址 - Compose UI调用入口
     */
    public void playerContent(String url, String flag) {
        Site site = VodConfig.get().getHome();
        if (site != null) {
            playerContent(site.getKey(), flag, url);
        }
    }

    // 🔥 原版FongMi_TV设计：获取全局搜索线程池
    private static synchronized top.cywin.onetv.movie.utils.PauseExecutor getSearchExecutor() {
        if (globalSearchExecutor == null || globalSearchExecutor.isShutdown()) {
            globalSearchExecutor = new top.cywin.onetv.movie.utils.PauseExecutor(10); // 原版设计：10个线程
            android.util.Log.d("VOD_FLOW", "[SEARCH_EXECUTOR_CREATE] 创建全局PauseExecutor搜索线程池，线程数: 10");
        }

        // 🔥 修复：确保搜索线程池处于运行状态
        globalSearchExecutor.resume();
        android.util.Log.d("VOD_FLOW", "[SEARCH_EXECUTOR_RESUME] 确保搜索线程池处于运行状态");

        return globalSearchExecutor;
    }

    // 🔥 原版FongMi_TV设计：暂停搜索
    public static void pauseSearch() {
        if (globalSearchExecutor != null) {
            globalSearchExecutor.pause();
            android.util.Log.d("VOD_FLOW", "[SEARCH_EXECUTOR_PAUSE] 暂停搜索线程池");
        }
    }

    // 🔥 原版FongMi_TV设计：恢复搜索
    public static void resumeSearch() {
        if (globalSearchExecutor != null) {
            globalSearchExecutor.resume();
            android.util.Log.d("VOD_FLOW", "[SEARCH_EXECUTOR_RESUME] 恢复搜索线程池");
        }
    }

    // 🔥 原版FongMi_TV设计：关闭搜索线程池（应用退出时调用）
    public static void shutdownSearchExecutor() {
        if (globalSearchExecutor != null) {
            globalSearchExecutor.shutdownNow();
            globalSearchExecutor = null;
            android.util.Log.d("VOD_FLOW", "[SEARCH_EXECUTOR_SHUTDOWN] 关闭全局搜索线程池");
        }
    }

    @Override
    protected void onCleared() {
        // 🔥 原版设计：只关闭实例级别的线程池，不关闭全局搜索线程池
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown(); // 优雅关闭，等待正在执行的任务完成
            try {
                // 等待最多5秒让任务完成
                if (!executor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    android.util.Log.w("VOD_FLOW", "[SITE_VM_CLEANUP] 内容线程池未能在5秒内关闭，强制关闭");
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                android.util.Log.w("VOD_FLOW", "[SITE_VM_CLEANUP] 等待内容线程池关闭时被中断");
                executor.shutdownNow();
            }
        }

        // 关闭播放器线程池
        if (playerExecutor != null && !playerExecutor.isShutdown()) {
            playerExecutor.shutdownNow();
        }

        // 注意：不关闭全局搜索线程池，因为它是静态的，可能被其他实例使用
        android.util.Log.d("VOD_FLOW", "[SITE_VM_CLEANUP] SiteViewModel清理完成，全局搜索线程池保持运行");
    }
}
