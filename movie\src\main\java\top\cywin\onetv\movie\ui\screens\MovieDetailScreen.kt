package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.graphics.Brush
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.Error
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import kotlinx.coroutines.delay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieDetailViewModel
import top.cywin.onetv.movie.viewmodel.DetailUiState
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import top.cywin.onetv.movie.ui.model.PlayFlag
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.model.Episode
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.player.VideoController
import top.cywin.onetv.movie.player.rememberVideoController
import top.cywin.onetv.movie.ui.components.PlayerControls
import top.cywin.onetv.movie.ui.components.PlayerTopInfo
import android.app.Activity
import android.util.Log
import coil.compose.AsyncImage
import androidx.activity.compose.BackHandler
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * OneTV Movie详情页面 - 使用完整播放器组件，参考原版FongMi_TV架构
 */
@Composable
fun MovieDetailScreen(
    vodId: String,
    siteKey: String = "",
    navController: NavController,
    vodPic: String? = null, // 🔥 海报修复：添加海报URL参数
    flowId: String? = null, // 🔥 FlowID修复：添加FlowID参数，建立完整日志链
    viewModel: MovieDetailViewModel = viewModel {
        MovieDetailViewModel()
    }
) {
    // 🔥 FlowID修复：确保FlowID不为null，建立完整日志链
    val globalFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getGlobalFlowId()
    val currentFlowId = flowId ?: globalFlowId ?: "DETAIL_${System.currentTimeMillis()}"

    // 🔥 调试日志：显示FlowID传递情况
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_FLOWID_DEBUG] 导航参数flowId=$flowId, 全局FlowID=$globalFlowId, 最终使用=$currentFlowId")
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_SCREEN_START] 电影详情页面启动")
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_PARAMS] vodId=$vodId, siteKey=$siteKey")
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_PIC_RECEIVED] 接收海报URL: $vodPic")

    // 🔥 关键修复：确保全局FlowID被设置
    top.cywin.onetv.movie.utils.VodFlowTracker.setGlobalFlowId(currentFlowId)
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_FLOWID_UPDATE] 设置全局FlowID: $currentFlowId")
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 🎬 播放器相关状态
    val context = LocalContext.current
    val activity = context as Activity
    val controller = rememberVideoController(activity)

    // 🔥 新增：设置片头片尾跳过回调 - 连接下一集播放功能
    LaunchedEffect(controller) {
        controller.onNextEpisodeCallback = {
            // 🔥 片尾自动跳过：播放下一集
            val currentEpisodes = uiState.episodes
            val currentIndex = currentEpisodes.indexOfFirst { it.name == uiState.currentEpisode?.name }
            if (currentIndex >= 0 && currentIndex < currentEpisodes.size - 1) {
                val nextEpisode = currentEpisodes[currentIndex + 1]
                Log.d("ONETV_DETAIL_SCREEN", "🔥 [片尾跳过] 自动播放下一集: ${nextEpisode.name}")
                viewModel.selectEpisode(nextEpisode)
            } else {
                Log.d("ONETV_DETAIL_SCREEN", "🔥 [片尾跳过] 已经是最后一集，无法自动播放下一集")
            }
        }
    }

    // 🎬 播放器显示模式：小屏（在详情页面中）↔ 全屏（占满整个屏幕）
    var isPlayerFullScreen by remember { mutableStateOf(false) }

    // 🔥 关键修复：防止从全屏返回时重新加载详情数据
    var hasLoadedDetail by remember { mutableStateOf(false) }

    // 🎬 处理返回键：全屏模式下返回小屏，小屏模式下返回上一页
    BackHandler {
        if (isPlayerFullScreen) {
            Log.d("ONETV_DETAIL_SCREEN", "🎬 [返回键] 从全屏模式返回小屏模式")
            isPlayerFullScreen = false

            // 🔥 关键修复：从全屏返回小屏时，强制清理错误状态，确保小屏播放器正常显示
            Log.d("ONETV_DETAIL_SCREEN", "🔥 [修复] 从全屏返回时强制清理错误状态，确保播放器正常显示")
            viewModel.clearError()

            // 🔥 额外修复：延迟再次清理，防止异步错误事件干扰
            CoroutineScope(Dispatchers.Main).launch {
                delay(100) // 延迟100ms
                viewModel.clearError()
                Log.d("ONETV_DETAIL_SCREEN", "🔥 [修复] 延迟清理错误状态完成")
            }
        } else {
            Log.d("ONETV_DETAIL_SCREEN", "🎬 [返回键] 从详情页面返回上一页")

            // 🔥 关键修复：退出电影详情页面时停止播放器，防止后台继续播放
            val currentFlowId = VodFlowTracker.getCurrentFlowId()
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [EXIT_DETAIL] 退出电影详情页面，停止播放器")
            VodFlowTracker.logFlowStep(currentFlowId, "EXIT_DETAIL", "退出电影详情页面")

            controller.release() // 释放播放器资源，停止播放
            navController.popBackStack()
        }
    }

    // 🔥 关键修复：只在首次加载时获取详情数据，避免从全屏返回时重新加载
    LaunchedEffect(vodId, siteKey) {
        if (!hasLoadedDetail) {
            Log.d("VOD_FLOW", "[FlowID:${currentFlowId ?: "UNKNOWN"}] [DETAIL_LOAD_START] 开始加载电影详情")
            Log.d("VOD_FLOW", "[FlowID:${currentFlowId ?: "UNKNOWN"}] [DETAIL_LOAD_PARAMS] vodId=$vodId, siteKey=$siteKey, vodPic=$vodPic")
            // 🔥 关键修复：传递当前FlowID和海报URL到ViewModel，建立完整日志链
            viewModel.loadMovieDetail(vodId, siteKey, currentFlowId, vodPic)
            hasLoadedDetail = true
        } else {
            Log.d("VOD_FLOW", "[FlowID:${currentFlowId ?: "UNKNOWN"}] [DETAIL_LOAD_SKIP] 详情数据已加载，跳过重新加载")
        }
    }

    // 🔥 关键修复：播放器状态管理，避免重复初始化
    var isPlayerReady by remember { mutableStateOf(false) }

    // 🔥 关键修复：防止相同播放URL重复触发播放
    var lastPlayedUrl by remember { mutableStateOf<String?>(null) }

    // 🔥 重置播放器状态当模式切换时
    LaunchedEffect(isPlayerFullScreen) {
        // 当切换模式时，重置播放器准备状态，让新模式重新初始化
        isPlayerReady = false
    }

    // 🎬 播放URL变化时开始播放 - 只在播放器准备好且有播放URL时启动播放
    LaunchedEffect(uiState.currentEpisode?.playUrl, isPlayerReady) {
        uiState.currentEpisode?.let { episode ->
            if (episode.playUrl.isNotEmpty() && isPlayerReady) {
                // 🔥 关键修复：防止相同URL重复播放
                if (episode.playUrl == lastPlayedUrl) {
                    Log.d("ONETV_DETAIL_SCREEN", "🎬 [播放URL重复] 跳过相同播放URL: ${episode.playUrl}")
                    return@LaunchedEffect
                }

                Log.d("ONETV_DETAIL_SCREEN", "🎬 [播放URL变化] 开始播放: ${episode.name}, URL: ${episode.playUrl}")

                // 记录已播放的URL
                lastPlayedUrl = episode.playUrl

                // 获取播放器实例并开始播放
                controller.mPlayers?.let { players ->
                    val result = top.cywin.onetv.movie.bean.Result().apply {
                        setPlayUrl(episode.playUrl)
                        setParse(0) // 不需要解析
                    }
                    players.start(result, false, 30000L) // 30秒超时
                    Log.d("ONETV_DETAIL_SCREEN", "🎬 [播放URL变化] 播放器启动成功")

                    // 🔥 修复：设置播放进度监听器，定期保存观看历史
                    controller.setOnTimeChangedListener { position, duration ->
                        // 通过ViewModel保存观看历史
                        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_PROGRESS_UPDATE] 播放进度更新: 位置=${position}ms, 时长=${duration}ms")

                        // 调用ViewModel的updatePlayProgress方法保存观看历史
                        viewModel.updatePlayProgress(position, duration)
                    }

                    // 🔥 修复：片头跳过现在在播放器STATE_READY时自动执行，无需手动调用
                    Log.d("ONETV_DETAIL_SCREEN", "🔥 [片头跳过] 片头跳过将在播放器准备完成后自动执行")
                } ?: run {
                    Log.e("ONETV_DETAIL_SCREEN", "🎬 [播放URL变化] 播放器实例为空，无法播放")
                }
            }
        }
    }

    // ✅ UI内容渲染 - 使用完整播放器组件
    if (isPlayerFullScreen) {
        // 🔥 全屏模式切换日志
        LaunchedEffect(isPlayerFullScreen) {
            val currentFlowId = VodFlowTracker.getCurrentFlowId()
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_MODE_FULLSCREEN", "切换到全屏播放模式")
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_MODE_FULLSCREEN] 播放器切换到全屏模式")
        }

        // 🎬 全屏播放器模式 - 使用完整的MoviePlayerScreen组件
        FullScreenPlayerMode(
            movieTitle = uiState.movie?.vodName ?: "未知电影",
            episode = uiState.currentEpisode,
            episodes = uiState.episodes,
            controller = controller,
            onBack = {
                val currentFlowId = VodFlowTracker.getCurrentFlowId()
                VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_MODE_SMALL", "从全屏模式返回小屏模式")
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_MODE_SMALL] 播放器从全屏模式返回小屏模式")
                isPlayerFullScreen = false
            },
            onEpisodeSelect = { episode ->
                // 🔥 关键修复：切换剧集时重置播放URL标记
                lastPlayedUrl = null
                viewModel.selectEpisode(episode)
            },
            onPlayerReady = { isPlayerReady = true }
        )
    } else {
        // 🔥 小屏模式切换日志
        LaunchedEffect(isPlayerFullScreen) {
            val currentFlowId = VodFlowTracker.getCurrentFlowId()
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_MODE_SMALL", "播放器处于小屏模式")
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_MODE_SMALL] 播放器处于小屏模式")
        }

        // 🎬 详情页面模式 - 包含小屏播放器
        DetailContent(
            uiState = uiState,
            controller = controller,
            onBack = {
                // 🔥 关键修复：从详情页面返回时停止播放器
                val currentFlowId = VodFlowTracker.getCurrentFlowId()
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [EXIT_DETAIL_BUTTON] 点击返回按钮退出电影详情页面")
                VodFlowTracker.logFlowStep(currentFlowId, "EXIT_DETAIL_BUTTON", "点击返回按钮退出")

                controller.release() // 释放播放器资源，停止播放
                navController.popBackStack()
            },
            onPlayerFullScreen = {
                val currentFlowId = VodFlowTracker.getCurrentFlowId()
                VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_MODE_FULLSCREEN", "从小屏模式切换到全屏模式")
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_MODE_FULLSCREEN] 播放器从小屏模式切换到全屏模式")
                isPlayerFullScreen = true
            },
            onToggleFavorite = { viewModel.toggleFavorite() },
            onFlagSelect = { flag -> viewModel.selectFlag(flag) },
            onEpisodeSelect = { episode ->
                // 🔥 关键修复：切换剧集时重置播放URL标记
                lastPlayedUrl = null
                viewModel.selectEpisode(episode)
            },
            onShowFlagSelector = { viewModel.showFlagSelector() },
            onHideFlagSelector = { viewModel.hideFlagSelector() },
            onShowEpisodeSelector = { viewModel.showEpisodeSelector() },
            onHideEpisodeSelector = { viewModel.hideEpisodeSelector() },
            onPlayerReady = { isPlayerReady = true }
        )
    }
}

@Composable
private fun DetailContent(
    uiState: DetailUiState,
    controller: VideoController,
    onBack: () -> Unit,
    onPlayerFullScreen: () -> Unit,
    onToggleFavorite: () -> Unit,
    onFlagSelect: (PlayFlag) -> Unit,
    onEpisodeSelect: (Episode) -> Unit,
    onShowFlagSelector: () -> Unit,
    onHideFlagSelector: () -> Unit,
    onShowEpisodeSelector: () -> Unit,
    onHideEpisodeSelector: () -> Unit,
    onPlayerReady: () -> Unit
) {
    // 🎬 统一的电影详情页面布局架构 - 所有状态都使用相同布局
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 🎬 主要内容区域 - 固定高度，不滚动
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // A区域（上左）+ B区域（上右）：电影信息 + 播放器
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.5f), // 占用50%的高度
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // A区域：电影详细信息（左侧）- 始终显示
                when {
                    uiState.isLoading -> {
                        // 加载状态下显示占位信息
                        LoadingMovieInfoSection(
                            modifier = Modifier.weight(0.55f)
                        )
                    }
                    uiState.error != null -> {
                        // 错误状态下显示基本信息
                        ErrorMovieInfoSection(
                            movieName = uiState.movie?.vodName ?: "加载失败",
                            modifier = Modifier.weight(0.55f)
                        )
                    }
                    else -> {
                        // 正常状态下显示完整信息
                        uiState.movie?.let { movie ->
                            MovieInfoSection(
                                movie = movie,
                                modifier = Modifier.weight(0.55f),
                                onToggleFavorite = onToggleFavorite,
                                isFavorite = uiState.isFavorite
                            )
                        }
                    }
                }

                // B区域：播放器区域（右侧）- 根据状态显示不同内容
                when {
                    uiState.isLoading -> {
                        // 在播放器区域显示加载状态
                        LoadingPlayerSection(
                            modifier = Modifier
                                .weight(0.45f)
                                .height(200.dp)
                        )
                    }
                    uiState.error != null -> {
                        // 在播放器区域显示错误信息
                        ErrorPlayerSection(
                            errorMessage = uiState.error ?: "未知错误",
                            onRetry = { /* 重试逻辑 */ },
                            onBack = onBack,
                            modifier = Modifier
                                .weight(0.45f)
                                .height(200.dp)
                        )
                    }
                    else -> {
                        // 正常状态下显示小屏播放器
                        if (uiState.episodes.isNotEmpty() && uiState.currentEpisode != null) {
                            SmallScreenPlayerSection(
                                episode = uiState.currentEpisode!!,
                                episodes = uiState.episodes,
                                controller = controller,
                                modifier = Modifier
                                    .weight(0.45f)
                                    .height(200.dp),
                                onFullScreen = onPlayerFullScreen,
                                onEpisodeSelect = onEpisodeSelect,
                                onPlayerReady = onPlayerReady
                            )
                        }
                    }
                }
            }

            // C区域：播放线路选择（中间）- 只在正常状态下显示
            if (!uiState.isLoading && uiState.error == null && uiState.playFlags.isNotEmpty()) {
                PlayFlagSection(
                    flags = uiState.playFlags,
                    selectedFlag = uiState.currentFlag,
                    onFlagSelect = onFlagSelect,
                    modifier = Modifier.height(60.dp)
                )
            }

            // D区域：选集播放（下方）- 只在正常状态下显示
            if (!uiState.isLoading && uiState.error == null && uiState.episodes.isNotEmpty()) {
                EpisodeSection(
                    episodes = uiState.episodes,
                    selectedEpisode = uiState.currentEpisode,
                    onEpisodeSelect = onEpisodeSelect,
                    onPlay = { episode, index ->
                        // 在新架构中，选择剧集就会自动在播放器中播放
                        onEpisodeSelect(episode)
                    },
                    modifier = Modifier.weight(0.35f)
                )
            }
        }
    }

}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingMovieInfoSection(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.height(200.dp),
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 电影基本信息区域 - 显示占位内容
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = "加载中...",
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "正在获取详情信息...",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 功能按钮区域 - 占位
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            repeat(3) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .background(
                            Color.Gray.copy(alpha = 0.3f),
                            RoundedCornerShape(16.dp)
                        )
                )
            }
        }
    }
}

@Composable
private fun ErrorMovieInfoSection(
    movieName: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.height(200.dp),
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 电影基本信息区域 - 显示基本信息
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = movieName,
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "详情加载失败，请重试",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                color = Color.Gray,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 功能按钮区域 - 禁用状态
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            repeat(3) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .background(
                            Color.Gray.copy(alpha = 0.2f),
                            RoundedCornerShape(16.dp)
                        )
                )
            }
        }
    }
}

@Composable
private fun LoadingPlayerSection(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "正在加载详情...",
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun ErrorPlayerSection(
    errorMessage: String,
    onRetry: () -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "错误",
                    tint = Color.Red,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = errorMessage,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Button(
                        onClick = onRetry
                    ) {
                        Text("重试")
                    }
                    Button(
                        onClick = onBack
                    ) {
                        Text("返回")
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}

@Composable
private fun MovieInfoSection(
    movie: MovieItem,
    modifier: Modifier = Modifier,
    onToggleFavorite: () -> Unit,
    isFavorite: Boolean
) {
    // 🎬 A区域：电影详细信息（参考架构图左侧区域）
    Column(
        modifier = modifier.height(200.dp), // 🔥 修复：固定高度与播放器对齐
        verticalArrangement = Arrangement.SpaceBetween // 🔥 修复：上下分布，功能按钮固定在底部
    ) {
        // 🎬 电影基本信息区域 - 按用户要求调整字体大小和显示内容
        Column(
            modifier = Modifier.weight(1f), // 占用剩余空间
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // 剧名 - 比下方信息字体大一号
            Text(
                text = movie.vodName,
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 来源 - 显示完整站点名称
            Text(
                text = "来源: ${getSiteDisplayName(movie.siteKey)}",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                color = MaterialTheme.colorScheme.primary
            )

            // 演员 - 清理特殊字符，只显示一行
            if (movie.vodActor.isNotEmpty()) {
                Text(
                    text = "演员: ${cleanActorDirectorName(movie.vodActor)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 导演 - 清理特殊字符
            if (movie.vodDirector.isNotEmpty()) {
                Text(
                    text = "导演: ${cleanActorDirectorName(movie.vodDirector)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 🎬 删除播放地址行 - 按用户要求留给简介更多空间

            // 内容简介 - 限制高度，为功能按钮留出空间
            if (movie.vodContent.isNotEmpty()) {
                Text(
                    text = "简介: ${movie.vodContent}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 3, // 减少行数，为按钮留空间
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 🔥 修复：功能按钮区域固定在底部，与播放器下边框齐平
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp), // 固定按钮区域高度
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
                // 快速搜索 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现快速搜索 */ },
                    label = {
                        Text(
                            text = "快速搜索",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 集数正/倒序 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现集数排序 */ },
                    label = {
                        Text(
                            text = "倒序",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Sort,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 加入收藏 - 使用FilterChip样式
                FilterChip(
                    onClick = onToggleFavorite,
                    label = {
                        Text(
                            text = if (isFavorite) "已收藏" else "收藏",
                            fontSize = 12.sp
                        )
                    },
                    selected = isFavorite,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            if (isFavorite) Icons.Default.Favorite else Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 内容简介 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 显示完整简介 */ },
                    label = {
                        Text(
                            text = "简介",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }
    }
}





@Composable
private fun InfoRow(label: String, value: String?) {
    value?.let {
        if (it.isNotEmpty()) {
            Row {
                Text(
                    text = "$label: ",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

// 🎬 C区域：播放线路选择（参考架构图中间区域）
@Composable
private fun PlayFlagSection(
    flags: List<PlayFlag>,
    selectedFlag: PlayFlag?,
    onFlagSelect: (PlayFlag) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"在线播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "在线播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 线路标签可左右滑动
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(flags) { flag ->
                FilterChip(
                    onClick = { onFlagSelect(flag) },
                    label = {
                        Text(
                            text = flag.flag ?: "未知线路",
                            fontSize = 12.sp
                        )
                    },
                    selected = selectedFlag == flag,
                    modifier = Modifier.height(36.dp)
                )
            }
        }
    }
}

// 🎬 D区域：选集播放（参考架构图下方区域）
@Composable
private fun EpisodeSection(
    episodes: List<Episode>,
    selectedEpisode: Episode?,
    onEpisodeSelect: (Episode) -> Unit,
    onPlay: (Episode, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"选集播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "选集播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 剧集网格布局 - 固定每行8个标签，平均分布
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 🎬 固定每行显示8个集数标签 - 按用户要求
            val itemsPerRow = 8
            val chunkedEpisodes = episodes.chunked(itemsPerRow)

            items(chunkedEpisodes) { rowEpisodes ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    rowEpisodes.forEachIndexed { localIndex, episode ->
                        val globalIndex = episodes.indexOf(episode)
                        EpisodeChip(
                            episode = episode,
                            isSelected = selectedEpisode?.let { selected ->
                                // 🔥 修复：使用结构化比较而不是引用比较，解决Episode.copy()导致的选中状态问题
                                selected.name == episode.name && selected.url == episode.url
                            } ?: false,
                            onClick = {
                                // 🔥 关键修复：只调用onEpisodeSelect，它会自动解析播放地址并更新内嵌播放器
                                // 不直接调用onPlay，避免立即跳转到全屏播放器
                                onEpisodeSelect(episode)
                            },
                            modifier = Modifier.weight(1f) // 平均分布，铺满一行
                        )
                    }

                    // 如果这一行不足8个，用空白填充
                    repeat(itemsPerRow - rowEpisodes.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

@Composable
private fun EpisodeChip(
    episode: Episode,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = episode.name,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 11.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal // 🔥 修复：选中时加粗
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center, // 🎬 文字左右居中
                modifier = Modifier.fillMaxWidth(), // 确保文字在整个标签宽度内居中
                color = if (isSelected) Color.White else LocalContentColor.current // 🔥 修复：选中时白色文字
            )
        },
        selected = isSelected,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary, // 🔥 修复：选中时主色调背景
            selectedLabelColor = Color.White, // 🔥 修复：选中时白色文字
            containerColor = MaterialTheme.colorScheme.surface, // 🔥 修复：未选中时表面色
            labelColor = MaterialTheme.colorScheme.onSurface // 🔥 修复：未选中时表面文字色
        ),
        border = if (isSelected) {
            FilterChipDefaults.filterChipBorder(
                enabled = true,
                selected = true,
                borderColor = MaterialTheme.colorScheme.primary,
                selectedBorderColor = MaterialTheme.colorScheme.primary,
                borderWidth = 2.dp // 🔥 修复：选中时加粗边框
            )
        } else {
            FilterChipDefaults.filterChipBorder(
                enabled = true,
                selected = false
            )
        },
        modifier = modifier
            .height(32.dp), // 调整高度以适应新布局
        leadingIcon = if (isSelected) {
            {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )
            }
        } else null
    )
}

// 🎬 工具函数：清理演员和导演名字中的特殊字符
private fun cleanActorDirectorName(name: String): String {
    if (name.isEmpty()) return name

    // 移除方括号及其内容，例如：[a=cr:{"id":"宿宇杰/{pg}
    val withoutBrackets = name.replace(Regex("\\[.*?\\]"), "")

    // 移除花括号及其内容
    val withoutBraces = withoutBrackets.replace(Regex("\\{.*?\\}"), "")

    // 移除其他特殊字符，保留中文、英文、数字和常见标点
    val cleaned = withoutBraces.replace(Regex("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s,，、·]"), "")

    // 清理多余的空格和逗号
    return cleaned.replace(Regex("\\s+"), " ")
                  .replace(Regex("[,，]+"), "，")
                  .trim()
}

// 🎬 工具函数：获取站点显示名称
private fun getSiteDisplayName(siteKey: String): String {
    return try {
        val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(siteKey)
        site?.name ?: siteKey.replace("onetv_", "").replace("_", " ")
    } catch (e: Exception) {
        Log.w("MovieDetailScreen", "获取站点名称失败: $siteKey", e)
        siteKey.replace("onetv_", "").replace("_", " ")
    }
}

// 🎬 全屏播放器模式 - 使用完整的播放器组件
@Composable
private fun FullScreenPlayerMode(
    movieTitle: String,
    episode: Episode?,
    episodes: List<Episode>,
    controller: VideoController,
    onBack: () -> Unit,
    onEpisodeSelect: (Episode) -> Unit,
    onPlayerReady: () -> Unit = {}
) {
    val context = LocalContext.current

    // 🎬 创建PlayerView
    val playerView = remember {
        androidx.media3.ui.PlayerView(context).apply {
            useController = false // 使用自定义控制器
            setShowBuffering(androidx.media3.ui.PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
        }
    }

    // 🔥 关键修复：避免不必要的重新初始化，只在真正需要时执行
    LaunchedEffect(controller) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FULLSCREEN_INIT] 开始绑定全屏播放器")

        // 🔥 关键修复：获取现有播放器实例，不重新初始化
        val existingPlayers = controller.getPlayers()
        if (existingPlayers != null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_REUSE] 复用现有播放器实例")

            // 🔥 关键：只切换PlayerView，不重新设置媒体项，保持播放进度
            existingPlayers.switchPlayerView(playerView)
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FULLSCREEN_BIND_SUCCESS] 全屏播放器绑定成功，播放进度已保持")

            // 🔥 通知播放器已准备好
            onPlayerReady()
        } else {
            // 如果没有现有播放器，才进行初始化
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_NEW] 创建新播放器实例")
            controller.initialize()?.let { players ->
                players.init(playerView)
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FULLSCREEN_BIND_SUCCESS] 全屏播放器初始化完成")
                onPlayerReady()
            } ?: run {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [FULLSCREEN_BIND_ERROR] 播放器初始化失败")
            }
        }
    }

    // 🔥 新增：监听playerView变化，确保播放器正确绑定到新的PlayerView
    LaunchedEffect(playerView) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        val existingPlayers = controller.getPlayers()
        if (existingPlayers != null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FULLSCREEN_VIEW_CHANGE] PlayerView变化，重新绑定")
            existingPlayers.switchPlayerView(playerView)
        }
    }

    // 🎬 控制栏显示状态和自动隐藏逻辑 - 对标原版FongMi_TV
    var isControlsVisible by remember { mutableStateOf(true) }
    var hideControlsJob by remember { mutableStateOf<Job?>(null) }

    // 🎬 自动隐藏控制栏的函数
    fun scheduleHideControls() {
        hideControlsJob?.cancel()
        hideControlsJob = CoroutineScope(Dispatchers.Main).launch {
            delay(5000) // 5秒后自动隐藏
            isControlsVisible = false
        }
    }

    // 🎬 显示控制栏的函数
    fun showControls() {
        isControlsVisible = true
        scheduleHideControls()
    }

    // 🎬 初始化时启动自动隐藏
    LaunchedEffect(Unit) {
        scheduleHideControls()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures {
                    // 🎬 点击屏幕切换控制栏显示状态
                    if (isControlsVisible) {
                        isControlsVisible = false
                        hideControlsJob?.cancel()
                    } else {
                        showControls()
                    }
                }
            }
        // 🔥 关键修复：移除contentAlignment，让子组件使用自己的对齐方式
    ) {
        // 🎬 播放器视图
        AndroidView(
            factory = { playerView },
            modifier = Modifier.fillMaxSize()
        )

        // 🔥 顶部电影信息显示 - 全屏模式下显示当前集数信息
        PlayerTopInfo(
            movieTitle = movieTitle,
            currentEpisode = episode?.name ?: "第01集",
            totalEpisodes = episodes.size,
            isVisible = isControlsVisible,
            modifier = Modifier.align(Alignment.TopCenter)
        )

        // 🎬 完整的播放器控制栏 - 使用原版的14个按钮，底部显示，5秒自动隐藏
        PlayerControls(
            controller = controller,
            episodes = episodes,
            currentEpisodeIndex = episodes.indexOfFirst { it.name == episode?.name }.takeIf { it >= 0 } ?: 0,
            isVisible = isControlsVisible, // 根据状态控制显示/隐藏
            onEpisodeSelect = { index ->
                if (index < episodes.size) {
                    onEpisodeSelect(episodes[index])
                }
                showControls() // 操作后重新显示控制栏
            },
            onRefresh = {
                // 🔥 连接VideoController的刷新功能
                controller.setOnResetCallback { replay ->
                    // 重新播放当前剧集
                    episode?.let { onEpisodeSelect(it) }
                }
                showControls() // 操作后重新显示控制栏
            },
            onChangeSource = {
                // 🔥 连接VideoController的换源功能
                controller.setOnChangeSourceCallback {
                    // 切换播放源逻辑 - 暂时留空，后续可以添加换源逻辑
                }
                showControls() // 操作后重新显示控制栏
            },
            modifier = Modifier.align(Alignment.BottomCenter) // 确保控制栏在底部
        )
    }
}

// 🎬 小屏播放器区域 - 在详情页面中的播放器
@Composable
private fun SmallScreenPlayerSection(
    episode: Episode,
    episodes: List<Episode>,
    controller: VideoController,
    modifier: Modifier = Modifier,
    onFullScreen: () -> Unit,
    onEpisodeSelect: (Episode) -> Unit,
    onPlayerReady: () -> Unit = {}
) {
    val context = LocalContext.current

    // 🎬 创建PlayerView
    val playerView = remember {
        androidx.media3.ui.PlayerView(context).apply {
            useController = false // 使用自定义控制器
            setShowBuffering(androidx.media3.ui.PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            // 🎬 点击播放器进入全屏模式
            setOnClickListener {
                Log.d("ONETV_SMALL_PLAYER", "🎬 [全屏点击] 用户点击小屏播放器，进入全屏模式")
                onFullScreen()
            }
        }
    }

    // 🔥 关键修复：避免不必要的重新初始化，只在真正需要时执行
    LaunchedEffect(controller) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SMALL_SCREEN_INIT] 开始绑定小屏播放器")

        // 🔥 关键修复：检查是否有现有播放器实例
        val existingPlayers = controller.getPlayers()
        if (existingPlayers != null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_REUSE] 复用现有播放器实例")

            // 🔥 关键：只切换PlayerView，不重新设置媒体项，保持播放进度
            existingPlayers.switchPlayerView(playerView)
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SMALL_SCREEN_BIND_SUCCESS] 小屏播放器绑定成功，播放进度已保持")

            // 🔥 通知播放器已准备好
            onPlayerReady()
        } else {
            // 如果没有现有播放器，才进行初始化
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_NEW] 创建新播放器实例")
            controller.initialize()?.let { players ->
                players.init(playerView)
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SMALL_SCREEN_BIND_SUCCESS] 小屏播放器初始化完成")
                onPlayerReady()
            } ?: run {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SMALL_SCREEN_BIND_ERROR] 播放器初始化失败")
            }
        }
    }

    // 🔥 新增：监听playerView变化，确保播放器正确绑定到新的PlayerView
    LaunchedEffect(playerView) {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        val existingPlayers = controller.getPlayers()
        if (existingPlayers != null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SMALL_SCREEN_VIEW_CHANGE] PlayerView变化，重新绑定")
            existingPlayers.switchPlayerView(playerView)
        }
    }

    Box(
        modifier = modifier.background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        // 🎬 播放器视图
        AndroidView(
            factory = { playerView },
            modifier = Modifier.fillMaxSize()
        )

        // 🎬 小屏播放器控制栏 - 简化版控制器
        SmallScreenPlayerControls(
            controller = controller,
            onFullScreen = onFullScreen,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}

// 🎬 小屏播放器控制栏 - 简化版控制器
@Composable
private fun SmallScreenPlayerControls(
    controller: VideoController,
    onFullScreen: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.6f),
                RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp, vertical = 6.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放/暂停按钮
        IconButton(
            onClick = {
                controller.initialize()?.let { players ->
                    if (players.isPlaying()) {
                        players.pause()
                    } else {
                        players.play()
                    }
                }
            },
            modifier = Modifier.size(36.dp)
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow, // 简化版，不显示动态状态
                contentDescription = "播放/暂停",
                tint = Color.White,
                modifier = Modifier.size(20.dp)
            )
        }

        // 全屏按钮
        IconButton(
            onClick = onFullScreen,
            modifier = Modifier.size(36.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Fullscreen,
                contentDescription = "全屏播放",
                tint = Color.White,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}



