package top.cywin.onetv.movie.adapter;

import android.util.Log;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import androidx.navigation.NavController;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.model.SiteViewModel;
import top.cywin.onetv.movie.navigation.MovieRoutes;
import top.cywin.onetv.movie.utils.VodFlowTracker;
// 🔥 添加缺失的导入
import top.cywin.onetv.movie.ui.model.VodConfigUrl;
import top.cywin.onetv.movie.bean.Config;
// 导入原版FongMi_TV事件类
import top.cywin.onetv.movie.event.ErrorEvent;
import top.cywin.onetv.movie.event.MovieIdTransformEvent;

// 导入UIEvents.kt中的Kotlin事件类
import top.cywin.onetv.movie.event.ConfigUpdateEvent;
import top.cywin.onetv.movie.event.ContentDetailEvent;
// 🔥 搜索相关事件导入已删除 - 现在由SiteViewModel直接处理，RepositoryAdapter不再中转搜索结果
import top.cywin.onetv.movie.event.PlayUrlParseStartEvent;
import top.cywin.onetv.movie.event.PlayUrlParseErrorEvent;
import top.cywin.onetv.movie.event.FavoriteUpdateEvent;
import top.cywin.onetv.movie.event.FavoriteListEvent;
import top.cywin.onetv.movie.event.HistoryUpdateEvent;
import top.cywin.onetv.movie.event.HistoryListEvent;
import top.cywin.onetv.movie.event.SiteListEvent;
import top.cywin.onetv.movie.event.SiteChangeEvent;
import top.cywin.onetv.movie.event.CloudDriveEvent;
import top.cywin.onetv.movie.event.LiveChannelEvent;
import top.cywin.onetv.movie.event.NavigationEvent;
// 🔥 完全直通设计：NavigateToSearchResultsEvent导入已删除
import top.cywin.onetv.movie.event.LivePlayEvent;
import top.cywin.onetv.movie.event.SettingsUpdateEvent;
import top.cywin.onetv.movie.event.ApiTestEvent;
// 🔥 添加新增事件类的导入
import top.cywin.onetv.movie.event.NavigateToLiveEvent;
import top.cywin.onetv.movie.event.ShowHistoryScreenEvent;
import top.cywin.onetv.movie.event.ShowFavoritesScreenEvent;
import top.cywin.onetv.movie.event.ShowSiteSelectorEvent;
import top.cywin.onetv.movie.event.ShowRouteSelectorEvent;
import top.cywin.onetv.movie.event.RouteChangeEvent;
import top.cywin.onetv.movie.event.ShowSearchScreenEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Repository适配器 - 完整的FongMi_TV接口适配器
 * 提供Compose UI与FongMi_TV系统之间的完整接口适配
 * 
 * 🎯 重要架构原则：
 * - ✅ 适配器只做调用转发 - 不实现业务逻辑
 * - ✅ 所有解析逻辑在FongMi_TV - 保持原有解析系统
 * - ✅ 所有业务逻辑在FongMi_TV - Keep、History、VodConfig等
 * - ❌ 适配器不重新实现功能 - 避免代码重复
 */
public class RepositoryAdapter {

    private static final String TAG = "RepositoryAdapter";

    // ✅ 添加SiteViewModel实例
    private final top.cywin.onetv.movie.model.SiteViewModel siteViewModel;

    // 🔥 原版FongMi_TV直接导航支持
    private NavController navController;

    // 🔥 关键修复：防止重复导航的状态管理
    private String lastNavigatedVodId = null;
    private String lastNavigatedSiteKey = null;
    private long lastNavigationTime = 0;
    private static final long NAVIGATION_COOLDOWN = 1000; // 1秒冷却时间

    // 🔥 搜索相关状态标志已删除 - 原版搜索直通设计不需要RepositoryAdapter管理搜索状态

    public RepositoryAdapter() {
        this.siteViewModel = new top.cywin.onetv.movie.model.SiteViewModel();

        // 🔧 修复：重新注册EventBus，现在有占位监听方法
        EventBus.getDefault().register(this);

        Log.d(TAG, "🏗️ RepositoryAdapter 初始化完成，EventBus已注册");
    }

    /**
     * 🔥 原版FongMi_TV直接导航支持：设置NavController
     */
    public void setNavController(NavController navController) {
        this.navController = navController;
        Log.d(TAG, "🔥 [原版直接导航] NavController已设置: " + (navController != null ? "成功" : "null"));
    }

    /**
     * 销毁适配器，取消EventBus注册
     */
    public void destroy() {
        try {
            EventBus.getDefault().unregister(this);
            Log.d(TAG, "🏗️ RepositoryAdapter 已销毁，EventBus已取消注册");
        } catch (Exception e) {
            Log.w(TAG, "⚠️ EventBus取消注册失败", e);
        }
    }

    // ===== 配置管理接口 =====

    /**
     * 加载配置文件
     */
    public void loadConfig() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [LOAD_CONFIG_START] 开始加载配置文件");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "LOAD_CONFIG_START", "开始加载配置文件");

        // 🔥 调试：使用VOD_FLOW打印调用栈，找出是什么触发了配置加载
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [LOAD_CONFIG_DEBUG] loadConfig调用栈:");
        for (int i = 0; i < Math.min(stackTrace.length, 8); i++) {
            Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [LOAD_CONFIG_STACK] " + i + ": " + stackTrace[i].toString());
        }

        try {
            // ✅ 检查VodConfig实例
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = VodConfig.get();
            Log.d(TAG, "🔄 [RepositoryAdapter] VodConfig实例: " + (vodConfig != null ? "存在" : "null"));

            // ✅ 检查Config对象
            top.cywin.onetv.movie.bean.Config config = vodConfig.getConfig();
            Log.d(TAG, "🔄 [RepositoryAdapter] Config对象: " + (config != null ? "存在" : "null"));

            // ✅ 检查是否有有效的配置URL
            String configUrl = VodConfig.getUrl();
            Log.d(TAG, "🔄 [RepositoryAdapter] 配置URL: " + (configUrl != null ? configUrl : "null"));

            // 🔥 修复：移除RepositoryAdapter层面的缓存检查，让VodConfig自己处理缓存
            // 这样可以确保线路切换时，VodConfig.clear()能够正确清除缓存，强制重新加载
            Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [LOAD_CONFIG_NO_CACHE] 跳过RepositoryAdapter缓存检查，交由VodConfig处理");

            // ✅ 修复配置URL检查逻辑 - 如果没有URL，部署默认配置
            if (configUrl == null || configUrl.isEmpty()) {
                Log.w(TAG, "⚠️ [RepositoryAdapter] 没有配置URL，部署默认OneTV官方接口");
                Log.d(TAG, "🚀 [RepositoryAdapter] 开始部署OneTV官方影视接口");

                // 部署默认的OneTV官方接口
                top.cywin.onetv.movie.config.VodConfigDeployer.deployOnetvApiConfig(null,
                        new top.cywin.onetv.movie.impl.Callback() {
                            @Override
                            public void success() {
                                Log.d(TAG, "✅ [RepositoryAdapter] OneTV官方接口部署成功(无参数)");
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                            }

                            @Override
                            public void success(String result) {
                                Log.d(TAG, "✅ [RepositoryAdapter] OneTV官方接口部署成功(有参数): " + result);
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                            }

                            @Override
                            public void error(String msg) {
                                Log.e(TAG, "❌ [RepositoryAdapter] OneTV官方接口部署失败: " + msg);
                                Log.d(TAG, "📡 [RepositoryAdapter] 发送WELCOME_SCREEN事件");
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                            }
                        });
                return;
            }

            Log.d(TAG, "🔄 使用配置URL: " + configUrl);

            // ✅ 创建Config对象
            top.cywin.onetv.movie.bean.Config newConfig = top.cywin.onetv.movie.bean.Config.find(configUrl, 0);
            Log.d(TAG, "🏗️ 创建Config对象: " + (newConfig != null ? "成功" : "失败"));

            if (newConfig != null) {
                Log.d(TAG, "📊 Config详情 - URL: " + newConfig.getUrl() + ", Name: " + newConfig.getName());
            }

            // ✅ 使用正确的VodConfig.load方法
            Log.d(TAG, "🔄 [RepositoryAdapter] 创建Callback对象");
            VodConfig.load(newConfig, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "🎉 [RepositoryAdapter] SUCCESS()回调被调用！");
                    handleSuccess("无参数success回调");
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "🎉 [RepositoryAdapter] SUCCESS(String)回调被调用！");
                    Log.d(TAG, "📊 回调结果: " + (result != null ? result : "null"));
                    handleSuccess(result);
                }

                private void handleSuccess(String notice) {
                    Log.d(TAG, "✅ 配置文件加载成功");
                    Log.d(TAG, "📊 当前站点数量: " + VodConfig.get().getSites().size());
                    Log.d(TAG, "📊 当前解析器数量: " + VodConfig.get().getParses().size());
                    Log.d(TAG, "🚀 发送ConfigUpdateEvent(success=true)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                    Log.d(TAG, "✅ ConfigUpdateEvent发送完成");
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "💥 [RepositoryAdapter] ERROR回调被调用！");
                    Log.e(TAG, "❌ 配置文件加载失败: " + msg);
                    // ✅ 如果加载失败，显示欢迎界面
                    Log.w(TAG, "⚠️ 配置加载失败，显示欢迎界面");
                    Log.d(TAG, "🚀 发送ConfigUpdateEvent(success=false)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                    Log.d(TAG, "✅ ConfigUpdateEvent发送完成");
                }
            });
            Log.d(TAG, "✅ [RepositoryAdapter] VodConfig.load调用完成，等待回调");
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置文件加载失败", e);
            // ✅ 如果出现异常，显示欢迎界面
            Log.w(TAG, "⚠️ 配置加载异常，显示欢迎界面");
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 解析配置URL
     */
    public void parseConfig(String configUrl) {
        Log.d(TAG, "🔄 解析配置URL: " + configUrl);
        try {
            // ✅ 创建Config对象并加载
            top.cywin.onetv.movie.bean.Config config = top.cywin.onetv.movie.bean.Config.find(configUrl, 0);
            VodConfig.load(config, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "✅ 配置URL解析完成(无参数)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "✅ 配置URL解析完成(有参数): " + result);
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "❌ 配置URL解析失败: " + msg);
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置URL解析失败", e);
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 刷新配置
     */
    public void refreshConfig() {
        Log.d(TAG, "🔄 刷新配置");
        try {
            VodConfig vodConfig = VodConfig.get();
            vodConfig.clear();
            vodConfig.load(new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "✅ 配置刷新完成(无参数)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, true, null));
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "✅ 配置刷新完成(有参数): " + result);
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, true, null));
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "❌ 配置刷新失败: " + msg);
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, false, msg));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置刷新失败", e);
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 重连Repository
     */
    public void reconnectRepositories() {
        Log.d(TAG, "🔄 重连Repository");
        try {
            VodConfig.get().init();
            Log.d(TAG, "✅ Repository重连成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ Repository重连失败", e);
        }
    }

    // ===== 内容获取接口 =====

    /**
     * 获取首页内容
     */
    public void getHomeContent() {
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.generateFlowId();
        top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "ADAPTER_HOME_CONTENT_START",
                "适配器开始获取首页内容");

        try {
            siteViewModel.homeContent();
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId,
                    "ADAPTER_HOME_CONTENT_REQUEST_SENT",
                    "首页内容请求已发送到SiteViewModel");
        } catch (Exception e) {
            top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "ADAPTER_HOME_CONTENT_ERROR",
                    "获取首页内容失败: " + e.getMessage());
            EventBus.getDefault().post(new ErrorEvent("首页内容获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    /**
     * 获取分类列表
     */
    public void getCategories() {
        Log.d(TAG, "🔄 获取分类列表");
        try {
            siteViewModel.homeContent();
            Log.d(TAG, "✅ 分类列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 分类列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("分类列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    /**
     * 获取分类内容
     */
    public void getContentList(String typeId, int page, Map<String, String> filters) {
        Log.d(TAG, "🔄 获取内容列表: typeId=" + typeId + ", page=" + page);
        try {
            siteViewModel.categoryContent(typeId, page, true, filters);
            Log.d(TAG, "✅ 内容列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 内容列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("内容列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    // 🔥 完全直通设计：handleMovieClick方法已删除
    // 现在所有电影点击都通过MovieClickHandler直接处理，不再通过RepositoryAdapter中转

    // 🔥 完全直通设计：handleSearchMode方法已删除
    // 现在搜索模式通过MovieClickHandler直接处理

    // 🔥 startSearchMode方法已删除 - 搜索逻辑现在直接通过SiteViewModel处理

    /**
     * 处理Push协议 - 提取真实URL并设置push_agent站点
     */
    private void handlePushProtocol(String pushUrl, String movieName, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 处理Push协议: " + pushUrl);

        // 提取真实URL（移除push://前缀）
        String realUrl = pushUrl.substring(7);
        String pushSiteKey = "push_agent";

        Log.d(TAG, "📊 [FongMi_TV兼容] Push协议转换: " + pushUrl + " -> " + realUrl + ", 站点: " + pushSiteKey);

        // 发送转换完成事件
        EventBus.getDefault().post(new MovieIdTransformEvent(realUrl, realUrl, pushSiteKey, movieName));
    }

    /**
     * 路径2：直接模式处理 - 对应原版FongMi_TV的getDetail()逻辑
     *
     * 实际例子：用户选择具体的电影版本 (真实ID: 55094)
     * → 重新启动VideoActivity → checkId()检测到真实ID → getDetail()
     */
    private void handleDirectMode(String vodId, String movieName, String siteKey) {
        Log.d(TAG, "✅ [FongMi_TV兼容] 路径2：直接模式处理");
        Log.d(TAG, "📺 [FongMi_TV兼容] 实际例子：使用真实ID获取详情");
        Log.d(TAG, "📺 [FongMi_TV兼容] 电影: \"" + movieName + "\" (真实ID: " + vodId + ")");
        Log.d(TAG, "📺 [FongMi_TV兼容] 原版getDetail()逻辑:");
        Log.d(TAG, "📺 [FongMi_TV兼容] 1. checkId()检测到真实ID: " + vodId);
        Log.d(TAG, "📺 [FongMi_TV兼容] 2. 直接调用getDetail()");
        Log.d(TAG, "📺 [FongMi_TV兼容] 3. detailContent() → 获取电影详情");
        Log.d(TAG, "📺 [FongMi_TV兼容] 4. setDetail() → 显示详情页面");
        Log.d(TAG, "🎯 [电影ID跟踪] 直接模式处理真实电影ID: " + vodId);

        // 🔥 关键：直接模式不需要搜索，直接获取详情
        // 对应原版: getDetail() → detailContent() → setDetail()
        getContentDetail(vodId, siteKey);
    }

    // 🔥 重要：删除错误的ID转换逻辑
    // 原版FongMi_TV中，msearch类型ID不需要转换，应该直接显示空页面
    // 真实电影ID也不需要转换，应该直接获取详情
    // 因此删除performIdTransformation和startMultiSiteSearch方法

    /**
     * 获取内容详情 - 完整的FongMi_TV兼容错误处理
     */
    public void getContentDetail(String vodId, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 获取内容详情: vodId=" + vodId + ", siteKey=" + siteKey);

        // 参数验证
        if (vodId == null || vodId.isEmpty()) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 无效的电影ID");
            EventBus.getDefault().post(new ContentDetailEvent(null, false, "电影ID不能为空"));
            return;
        }

        // 🔥 删除临时ID检查逻辑
        // 原版FongMi_TV中不存在临时ID概念，所有ID都是真实的功能标识

        if (vodId.startsWith("msearch:")) {
            Log.w(TAG, "⚠️ [FongMi_TV兼容] msearch类型ID无法直接获取详情: " + vodId);
            handleMsearchIdError(vodId, siteKey);
            return;
        }

        try {
            // 站点选择逻辑
            if (siteKey != null && !siteKey.isEmpty()) {
                Log.d(TAG, "🔑 [FongMi_TV兼容] 使用指定站点: " + siteKey);
                siteViewModel.detailContent(siteKey, vodId);
            } else {
                Log.d(TAG, "🏠 [FongMi_TV兼容] 使用默认站点");
                siteViewModel.detailContent(vodId);
            }
            Log.d(TAG, "✅ [FongMi_TV兼容] 内容详情请求已发送");

        } catch (IllegalArgumentException e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 参数错误: " + e.getMessage());
            handleParameterError(vodId, siteKey, e);
        } catch (RuntimeException e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 运行时错误: " + e.getMessage());
            handleRuntimeError(vodId, siteKey, e);
        } catch (Exception e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 未知错误: " + e.getMessage(), e);
            handleUnknownError(vodId, siteKey, e);
        }
    }

    // 🔥 删除handleTempIdError方法
    // 原版FongMi_TV中不存在临时ID概念

    /**
     * 处理msearch类型ID错误
     */
    private void handleMsearchIdError(String msearchId, String siteKey) {
        String errorMsg = "搜索类型ID无法直接获取详情，请从搜索结果中选择";
        Log.w(TAG, "⚠️ [FongMi_TV兼容] " + errorMsg + ": " + msearchId);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理参数错误
     */
    private void handleParameterError(String vodId, String siteKey, IllegalArgumentException e) {
        String errorMsg = "参数错误: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理运行时错误
     */
    private void handleRuntimeError(String vodId, String siteKey, RuntimeException e) {
        String errorMsg = "系统错误: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理未知错误
     */
    private void handleUnknownError(String vodId, String siteKey, Exception e) {
        String errorMsg = "获取电影详情失败: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    // 🔥 搜索相关方法已删除 - 现在直接调用SiteViewModel，不再通过RepositoryAdapter中转

    /**
     * 占位EventBus监听方法 - 确保EventBus注册不会失败
     * 现在搜索结果直接从SiteViewModel发送到UI，不需要在RepositoryAdapter中转发
     */
    @org.greenrobot.eventbus.Subscribe(threadMode = org.greenrobot.eventbus.ThreadMode.MAIN)
    public void onPlaceholderEvent(Object event) {
        // 占位方法，不做任何处理
        // 这个方法确保EventBus能够找到@Subscribe注解的方法
    }

    /**
     * 模拟Intent参数替换 - 对应原版FongMi_TV的INTENT_PARAM_REPLACE逻辑
     */
    private void simulateIntentParamReplace(String realId, String movieName, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 模拟Intent参数替换");
        Log.d(TAG, "📺 [FongMi_TV兼容] 对应原版[INTENT_PARAM_REPLACE]: ID msearch:home→" + realId + ", 站点→" + siteKey);
        Log.d(TAG, "🎯 [FongMi_TV兼容] 模拟重新启动VideoActivity，使用真实ID: " + realId);
        Log.d(TAG, "🔄 [FongMi_TV兼容] 这次checkId()会检测到真实ID，走getDetail()路径");

        // 直接调用直接模式处理，模拟重新启动VideoActivity的效果
        handleDirectMode(realId, movieName, siteKey);
    }

    /**
     * 获取推荐内容
     */
    public void getRecommendContent() {
        Log.d(TAG, "🔄 获取推荐内容");
        try {
            // ✅ 修复：使用homeContent()获取推荐内容，而不是空关键词搜索
            // 因为很多站点不支持空关键词搜索，但homeContent()会返回首页推荐内容
            siteViewModel.homeContent();
            Log.d(TAG, "✅ 推荐内容请求已发送（通过homeContent）");
        } catch (Exception e) {
            Log.e(TAG, "❌ 推荐内容获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("推荐内容获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    // ===== 播放相关接口 =====

    /**
     * 解析播放地址
     */
    public void parsePlayUrl(String url, String siteKey, String flag) {
        // 🔥 获取当前FlowID用于日志跟踪
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null) {
            currentFlowId = "UNKNOWN";
        }

        android.util.Log.d("VOD_FLOW",
                "[FlowID:" + currentFlowId + "] [REPOSITORY_PARSE_START] RepositoryAdapter.parsePlayUrl被调用");
        android.util.Log.d("-VOD_FLOW",
                "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                        + currentFlowId + "] [REPOSITORY_PARSE] 参数: url=" + url + ", siteKey=" + siteKey + ", flag="
                        + flag);

        try {
            EventBus.getDefault().post(new PlayUrlParseStartEvent("", url, flag));

            // 🔥 修复：使用正确的参数顺序调用SiteViewModel.playerContent(key, flag, id)
            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [REPOSITORY_CALL_SITE] 即将调用SiteViewModel.playerContent");
            android.util.Log.d("-VOD_FLOW",
                    "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                            + currentFlowId + "] [REPOSITORY_CALL_SITE] 调用SiteViewModel解析播放地址");

            siteViewModel.playerContent(siteKey, flag, url);

            android.util.Log.d("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [REPOSITORY_CALL_COMPLETE] SiteViewModel.playerContent调用完成");
            android.util.Log.d("-VOD_FLOW",
                    "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                            + currentFlowId + "] [REPOSITORY_CALL_COMPLETE] SiteViewModel调用完成，等待异步结果");
        } catch (Exception e) {
            android.util.Log.e("VOD_FLOW",
                    "[FlowID:" + currentFlowId + "] [REPOSITORY_PARSE_ERROR] 播放地址解析失败: " + e.getMessage());
            android.util.Log.e("-VOD_FLOW",
                    "│ [" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date()) + "] [FlowID:"
                            + currentFlowId + "] [REPOSITORY_PARSE_ERROR] 解析失败");
            EventBus.getDefault().post(new PlayUrlParseErrorEvent("", e.getMessage()));
        }
    }

    /**
     * 切换播放线路
     */
    public void switchLine(String flag, String url) {
        Log.d(TAG, "🔄 切换播放线路: flag=" + flag + ", url=" + url);
        try {
            // ✅ 重新解析播放地址
            parsePlayUrl(url, "", flag);
            Log.d(TAG, "✅ 线路切换请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 线路切换失败", e);
            EventBus.getDefault().post(new ErrorEvent("", ErrorEvent.Type.PARSE, "线路切换失败: " + e.getMessage()));
        }
    }

    // ===== 收藏管理接口 =====

    /**
     * 添加收藏
     */
    public void addToFavorites(top.cywin.onetv.movie.bean.Vod movie) {
        Log.d(TAG, "🔄 添加收藏: " + movie.getVodName());
        try {
            // ✅ 手动创建Keep对象
            top.cywin.onetv.movie.bean.Keep keep = new top.cywin.onetv.movie.bean.Keep();
            keep.setKey(
                    movie.getSite().getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + movie.getVodId());
            keep.setCid(VodConfig.getCid());
            keep.setSiteName(movie.getSite().getName());
            keep.setVodPic(movie.getVodPic());
            keep.setVodName(movie.getVodName());
            keep.setCreateTime(System.currentTimeMillis());
            keep.setType(0); // 0 for VOD
            keep.save();
            Log.d(TAG, "✅ 收藏添加成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(movie.getVodId(), true, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 收藏添加失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(movie.getVodId(), true, false));
        }
    }

    /**
     * 移除收藏
     */
    public void removeFromFavorites(String vodId, String siteKey) {
        Log.d(TAG, "🔄 移除收藏: " + vodId);
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            top.cywin.onetv.movie.bean.Keep.delete(vodId);
            Log.d(TAG, "✅ 收藏移除成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 收藏移除失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, false));
        }
    }

    /**
     * 检查收藏状态
     */
    public boolean isFavorite(String vodId, String siteKey) {
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            return top.cywin.onetv.movie.bean.Keep.exist(vodId);
        } catch (Exception e) {
            Log.e(TAG, "❌ 检查收藏状态失败", e);
            return false;
        }
    }

    // ===== 历史记录接口 =====

    /**
     * 获取观看历史列表
     */
    public void getWatchHistoryList() {
        Log.d(TAG, "🔄 获取观看历史列表");
        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            List<top.cywin.onetv.movie.bean.History> histories = top.cywin.onetv.movie.bean.History.get();
            Log.d(TAG, "✅ 获取到历史记录: " + histories.size() + "条");

            // 通过EventBus发送历史记录事件
            EventBus.getDefault().post(new HistoryListEvent(histories, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取观看历史列表失败", e);
            EventBus.getDefault().post(new HistoryListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 获取观看历史
     */
    public top.cywin.onetv.movie.bean.History getWatchHistory(String vodId, String siteKey) {
        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            return top.cywin.onetv.movie.bean.History.find(vodId);
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取观看历史失败", e);
            return null;
        }
    }

    /**
     * 保存观看历史
     */
    public void saveWatchHistory(String vodId, String vodName, long position, long duration) {
        saveWatchHistory(vodId, vodName, "", position, duration); // 调用带海报参数的重载方法
    }

    /**
     * 保存观看历史（包含海报URL）
     * 🔥 修复：添加海报URL参数，确保历史记录包含完整的电影信息
     * 🎯 重要：此方法仅用于movie模块的观看历史，不影响TV模块的观看记录
     * TV模块使用独立的SupabaseWatchHistorySessionManager系统
     */
    public void saveWatchHistory(String vodId, String vodName, String vodPic, long position, long duration) {
        // 🔥 修复：添加详细的VOD_FLOW日志跟踪，对标原版FongMi_TV
        String currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [HISTORY_TIME_UPDATE] 更新观看历史时间 位置: " + position + ", 时长: "
                + duration + ", 电影: " + vodName);
        Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [HISTORY_SAVE_START] 开始保存观看历史到数据库");

        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            String siteKey = getSite() != null ? getSite().getKey() : "default";
            int cid = VodConfig.getCid();
            // 🔥 修复：使用原版FongMi_TV的正确格式：站点@@@vodId@@@cid
            String historyKey = siteKey + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId
                    + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + cid;

            Log.d("VOD_FLOW", "[HISTORY_UPDATE_START] 开始更新观看历史 Key: " + historyKey + ", 电影: " + vodName + ", 当前位置: "
                    + position + ", 当前时长: " + duration);

            top.cywin.onetv.movie.bean.History history = top.cywin.onetv.movie.bean.History.find(historyKey);
            if (history == null) {
                history = new top.cywin.onetv.movie.bean.History();
                history.setKey(historyKey);
                history.setVodName(vodName);
                history.setCid(VodConfig.getCid());
                Log.d("VOD_FLOW", "[HISTORY_CREATE] 创建新的观看历史记录: " + historyKey);
            } else {
                Log.d("VOD_FLOW", "[HISTORY_UPDATE] 更新已存在的观看历史记录: " + historyKey);
            }

            // 🔥 修复：设置海报URL，确保历史记录包含海报信息
            if (vodPic != null && !vodPic.isEmpty()) {
                history.setVodPic(vodPic);
                Log.d("VOD_FLOW", "[HISTORY_PIC_UPDATE] 更新观看历史海报: " + vodPic);
            } else {
                Log.w("VOD_FLOW", "[HISTORY_PIC_WARNING] 海报URL为空，保持原有海报信息");
            }

            history.setPosition(position);
            history.setDuration(duration);
            history.setCreateTime(System.currentTimeMillis());

            // 🔥 对标原版FongMi_TV的HISTORY_SAVE日志
            Log.i("VOD_FLOW", "[HISTORY_SAVE] 保存观看历史到数据库 Key: " + historyKey + ", 电影: " + vodName + ", 站点: " + cid
                    + ", 位置: " + position + ", 时长: " + duration + ", 海报: " + vodPic);

            history.save();

            // 🔥 对标原版FongMi_TV的HISTORY_SAVE_SUCCESS日志
            Log.i("VOD_FLOW", "[HISTORY_SAVE_SUCCESS] 观看历史保存成功 Key: " + historyKey);
            Log.d("VOD_FLOW", "[HISTORY_UPDATE_COMPLETE] 观看历史更新完成 Key: " + historyKey);
            Log.d("VOD_FLOW", "[FlowID:" + currentFlowId + "] [HISTORY_SAVE_COMPLETE] 观看历史保存完成");

            EventBus.getDefault().post(new HistoryUpdateEvent(vodId, position, duration, true));
        } catch (Exception e) {
            Log.e("VOD_FLOW", "[FlowID:" + currentFlowId + "] [HISTORY_SAVE_ERROR] 观看历史保存失败: " + e.getMessage(), e);
            EventBus.getDefault().post(new HistoryUpdateEvent(vodId, position, duration, false));
        }
    }

    // ===== 收藏功能接口 =====

    /**
     * 获取收藏列表
     */
    public void getFavoriteList() {
        Log.d(TAG, "🔄 获取收藏列表");
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            List<top.cywin.onetv.movie.bean.Keep> favorites = top.cywin.onetv.movie.bean.Keep.getVod();
            Log.d(TAG, "✅ 获取到收藏记录: " + favorites.size() + "条");

            // 通过EventBus发送收藏列表事件
            EventBus.getDefault().post(new FavoriteListEvent(favorites, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取收藏列表失败", e);
            EventBus.getDefault().post(new FavoriteListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 添加收藏
     */
    public void addToFavorite(String vodId, String vodName, String vodPic) {
        Log.d(TAG, "🔄 添加收藏: " + vodName);
        try {
            top.cywin.onetv.movie.bean.Site currentSite = getSite();
            if (currentSite == null) {
                Log.e(TAG, "❌ 当前站点为空，无法添加收藏");
                return;
            }

            String keepKey = currentSite.getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId;
            top.cywin.onetv.movie.bean.Keep keep = new top.cywin.onetv.movie.bean.Keep();
            keep.setKey(keepKey);
            keep.setSiteName(currentSite.getName());
            keep.setVodName(vodName);
            keep.setVodPic(vodPic);
            keep.setType(0); // 0表示点播收藏
            keep.setCreateTime(System.currentTimeMillis());
            keep.save(VodConfig.getCid());

            Log.d(TAG, "✅ 收藏添加成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, true, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 添加收藏失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, true, false));
        }
    }

    /**
     * 移除收藏
     */
    public void removeFromFavorite(String vodId) {
        Log.d(TAG, "🔄 移除收藏: " + vodId);
        try {
            top.cywin.onetv.movie.bean.Site currentSite = getSite();
            if (currentSite == null) {
                Log.e(TAG, "❌ 当前站点为空，无法移除收藏");
                return;
            }

            String keepKey = currentSite.getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId;
            top.cywin.onetv.movie.bean.Keep.delete(keepKey);

            Log.d(TAG, "✅ 收藏移除成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 移除收藏失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, false));
        }
    }

    // ===== 站点管理接口 =====

    /**
     * 获取站点列表
     */
    public void getSiteList() {
        Log.d(TAG, "🔄 获取站点列表");
        try {
            List<top.cywin.onetv.movie.bean.Site> sites = VodConfig.get().getSites();
            Log.d(TAG, "✅ 获取到站点: " + sites.size() + "个");

            // 通过EventBus发送站点列表事件
            EventBus.getDefault().post(new SiteListEvent(sites, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取站点列表失败", e);
            EventBus.getDefault().post(new SiteListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 获取当前站点
     */
    public top.cywin.onetv.movie.bean.Site getCurrentSite() {
        try {
            return VodConfig.get().getHome();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取当前站点失败", e);
            return null;
        }
    }

    /**
     * 切换站点
     */
    public void switchSite(String siteKey) {
        Log.d(TAG, "🔄 切换站点: " + siteKey);
        try {
            top.cywin.onetv.movie.bean.Site site = VodConfig.get().getSite(siteKey);
            if (site != null) {
                VodConfig.get().setHome(site);
                Log.d(TAG, "✅ 站点切换成功: " + site.getName());
                EventBus.getDefault().post(new SiteChangeEvent(site, true));

                // 重新加载首页内容
                getHomeContent();
            } else {
                Log.e(TAG, "❌ 站点不存在: " + siteKey);
                EventBus.getDefault().post(new SiteChangeEvent(null, false));
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ 站点切换失败", e);
            EventBus.getDefault().post(new SiteChangeEvent(null, false));
        }
    }

    // ===== 云盘相关接口 =====

    /**
     * 加载云盘配置列表
     */
    public void loadCloudDriveConfigs() {
        Log.d(TAG, "🔄 加载云盘配置列表");
        try {
            // 这里需要根据实际的FongMi_TV云盘实现进行调用
            // List<CloudDriveConfig> configs = CloudDriveManager.getConfigs();
            // EventBus.getDefault().post(new CloudDriveConfigEvent(configs, true));
            Log.d(TAG, "✅ 云盘配置列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 云盘配置列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("云盘配置列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK));
        }
    }

    /**
     * 获取云盘文件列表
     */
    public void getCloudFiles(String driveId, String path) {
        Log.d(TAG, "🔄 获取云盘文件列表: driveId=" + driveId + ", path=" + path);
        try {
            // 这里需要根据实际的FongMi_TV云盘实现进行调用
            // CloudDriveManager.getFiles(driveId, path, callback);
            Log.d(TAG, "✅ 云盘文件列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 云盘文件列表获取失败", e);
            EventBus.getDefault().post(new CloudDriveEvent(driveId, null, path, false));
        }
    }

    // ===== 直播相关接口 =====

    /**
     * 获取直播频道列表
     */
    public void getLiveChannels(String group) {
        Log.d(TAG, "🔄 获取直播频道列表: group=" + group);
        try {
            // 这里需要根据实际的FongMi_TV直播实现进行调用
            // LiveViewModel.get().getChannels(group);
            Log.d(TAG, "✅ 直播频道列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 直播频道列表获取失败", e);
            EventBus.getDefault().post(new LiveChannelEvent(null, group, false));
        }
    }

    /**
     * 解析直播播放地址
     */
    public void parseLivePlayUrl(String channelUrl, String channelName) {
        Log.d(TAG, "🔄 解析直播播放地址: " + channelName);
        try {
            // 这里需要根据实际的FongMi_TV直播实现进行调用
            // LiveViewModel.get().parsePlayUrl(channelUrl, callback);
            Log.d(TAG, "✅ 直播播放地址解析请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 直播播放地址解析失败", e);
            EventBus.getDefault().post(new LivePlayEvent("", channelName, false));
        }
    }

    // ===== 设置相关接口 =====

    /**
     * 更新设置
     */
    public void updateSetting(String key, Object value) {
        Log.d(TAG, "🔄 更新设置: " + key + " = " + value);
        try {
            // 这里需要根据实际的FongMi_TV设置实现进行调用
            // Setting.put(key, value);
            Log.d(TAG, "✅ 设置更新成功");
            EventBus.getDefault().post(new SettingsUpdateEvent(key, value, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 设置更新失败", e);
            EventBus.getDefault().post(new SettingsUpdateEvent(key, value, false));
        }
    }

    // ===== 网络相关接口 =====

    /**
     * 测试API连接
     */
    public void testApiConnection(String url) {
        Log.d(TAG, "🔄 测试API连接: " + url);
        try {
            long startTime = System.currentTimeMillis();
            // 这里需要根据实际的FongMi_TV网络实现进行调用
            // OkHttp.get().newCall(request).execute();
            long responseTime = System.currentTimeMillis() - startTime;
            Log.d(TAG, "✅ API连接测试成功");
            EventBus.getDefault().post(new ApiTestEvent(url, true, responseTime, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ API连接测试失败", e);
            EventBus.getDefault().post(new ApiTestEvent(url, false, 0, e.getMessage()));
        }
    }

    /**
     * 检查系统状态
     */
    public boolean isSystemReady() {
        try {
            VodConfig vodConfig = VodConfig.get();
            return vodConfig != null && !vodConfig.getSites().isEmpty();
        } catch (Exception e) {
            Log.e(TAG, "❌ 系统状态检查失败", e);
            return false;
        }
    }

    // ===== 配置管理方法 =====

    /**
     * 获取VodConfig实例
     */
    public top.cywin.onetv.movie.api.config.VodConfig getVodConfig() {
        return top.cywin.onetv.movie.api.config.VodConfig.get();
    }

    /**
     * 测试连接
     */
    public boolean testConnection(String url, String apiKey) {
        try {
            // ✅ 使用FongMi_TV现有的网络测试逻辑
            long startTime = System.currentTimeMillis();
            String response = com.github.catvod.net.OkHttp.string(url);
            long responseTime = System.currentTimeMillis() - startTime;
            boolean result = response != null && !response.isEmpty();
            EventBus.getDefault().post(new ApiTestEvent(url, result, responseTime, result ? null : "连接失败"));
            return result;
        } catch (Exception e) {
            Log.e(TAG, "连接测试失败", e);
            EventBus.getDefault().post(new ApiTestEvent(url, false, 0, e.getMessage()));
            return false;
        }
    }

    /**
     * 清除配置缓存
     */
    public void clearConfigCache() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            vodConfig.clear();
            Log.d(TAG, "✅ 配置缓存清除成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置缓存清除失败", e);
        }
    }

    // 🔥 parseRouteConfig方法已删除，统一使用switchRoute方法进行线路切换

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        try {
            // ✅ 清除FongMi_TV的所有缓存
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            vodConfig.clear();

            // 清除数据库缓存
            top.cywin.onetv.movie.database.AppDatabase.get().clearAllTables();

            Log.d(TAG, "✅ 所有缓存清除成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 清除所有缓存失败", e);
        }
    }

    // ===== 播放历史管理方法 =====

    /**
     * 保存播放历史
     */
    public void savePlayHistory(String vodId, String siteKey, int episodeIndex, long position, long duration) {
        try {
            top.cywin.onetv.movie.bean.History history = new top.cywin.onetv.movie.bean.History();
            history.setKey(vodId);
            history.setCid(Integer.parseInt(siteKey));
            history.setPosition(position);
            history.setDuration(duration);
            history.setCreateTime(System.currentTimeMillis());
            history.save();

            Log.d(TAG, "✅ 播放历史保存成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 播放历史保存失败", e);
        }
    }

    /**
     * 获取当前VOD ID
     */
    public String getVodId() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            return vodConfig.getHome().getKey();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取VOD ID失败", e);
            return "";
        }
    }

    /**
     * 获取当前站点
     */
    public top.cywin.onetv.movie.bean.Site getSite() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            return vodConfig.getHome();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取当前站点失败", e);
            return null;
        }
    }

    /**
     * 获取分类名称
     */
    public String getCategoryName(String typeId) {
        try {
            // 通过SiteViewModel获取分类信息
            top.cywin.onetv.movie.MovieApp movieApp = top.cywin.onetv.movie.MovieApp.Companion.getInstance();
            top.cywin.onetv.movie.model.SiteViewModel siteViewModel = movieApp.getSiteViewModel();
            // categoryContent方法返回void，不能赋值给Result
            // 使用简化处理方式

            // 简化处理，直接返回typeId作为分类名称
            return typeId;
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取分类名称失败", e);
            return "未知分类";
        }
    }

    /**
     * 🔥 原版FongMi_TV直接导航：监听ContentDetailEvent并直接跳转
     * 原版流程：电影点击 → VideoActivity.getDetail() → 直接页面跳转
     * 我们的流程：电影点击 → RepositoryAdapter → 直接navController.navigate()
     * 🔥 关键修复：防止重复导航造成无限循环
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onContentDetailEvent(ContentDetailEvent event) {
        Log.d(TAG, "📡 [原版直接导航] RepositoryAdapter收到ContentDetailEvent: success=" + event.getSuccess());

        if (event.getSuccess() && event.getVod() != null) {
            String vodId = event.getVod().getVodId();
            String siteKey = event.getVod().getSiteKey();
            long currentTime = System.currentTimeMillis();

            // 🔥 关键修复：检查是否为重复导航
            if (isRepeatedNavigation(vodId, siteKey, currentTime)) {
                Log.d(TAG, "🚫 [重复导航修复] 忽略重复导航请求: vodId=" + vodId + ", siteKey=" + siteKey);
                return;
            }

            // 🔥 新增修复：检查当前是否已经在详情页面
            if (navController != null) {
                try {
                    // 获取当前路由
                    String currentRoute = navController.getCurrentDestination() != null
                            ? navController.getCurrentDestination().getRoute()
                            : "";

                    // 如果当前已经在详情页面，不再执行导航
                    if (currentRoute != null && currentRoute.contains("movie_detail")) {
                        Log.d(TAG, "🚫 [搜索结果修复] 当前已在详情页面，跳过RepositoryAdapter导航");
                        Log.d(TAG, "📍 [搜索结果修复] 当前路由: " + currentRoute);
                        return;
                    }
                } catch (Exception e) {
                    Log.w(TAG, "⚠️ [搜索结果修复] 获取当前路由失败，继续执行导航", e);
                }
            }

            Log.d(TAG, "🔥 [原版直接导航] 采用原版FongMi_TV直接跳转模式！");
            Log.d(TAG, "✅ [原版直接导航] 详情获取成功，直接跳转到详情页");
            Log.d(TAG, "🎯 [电影ID跟踪] 详情数据: vodId=" + vodId + ", name=" + event.getVod().getVodName());

            // 🔥 原版FongMi_TV直接导航：直接调用navController.navigate()
            if (navController != null) {
                try {
                    String route = MovieRoutes.INSTANCE.detail(vodId, siteKey, null, null);
                    Log.d(TAG, "🔗 [原版直接导航] 导航路由: " + route);

                    navController.navigate(route);

                    // 🔥 关键修复：记录导航状态，防止重复导航
                    updateNavigationState(vodId, siteKey, currentTime);

                    Log.d(TAG, "✅ [原版直接导航] 直接导航成功！");
                    Log.d(TAG, "🎯 [电影ID跟踪] 成功跳转到详情页面");
                } catch (Exception e) {
                    Log.e(TAG, "❌ [原版直接导航] 直接导航失败", e);
                }
            } else {
                Log.w(TAG, "⚠️ [原版直接导航] NavController未设置，无法导航");
            }

        } else {
            Log.w(TAG, "⚠️ [原版直接导航] 详情获取失败: " + event.getErrorMessage());
        }
    }

    /**
     * 🔥 关键修复：检查是否为重复导航
     */
    private boolean isRepeatedNavigation(String vodId, String siteKey, long currentTime) {
        // 检查是否为相同的导航请求
        boolean sameRequest = vodId != null && vodId.equals(lastNavigatedVodId)
                && siteKey != null && siteKey.equals(lastNavigatedSiteKey);

        // 检查是否在冷却时间内
        boolean withinCooldown = (currentTime - lastNavigationTime) < NAVIGATION_COOLDOWN;

        return sameRequest && withinCooldown;
    }

    /**
     * 🔥 关键修复：更新导航状态
     */
    private void updateNavigationState(String vodId, String siteKey, long currentTime) {
        lastNavigatedVodId = vodId;
        lastNavigatedSiteKey = siteKey;
        lastNavigationTime = currentTime;
        Log.d(TAG, "🔥 [重复导航修复] 更新导航状态: vodId=" + vodId + ", siteKey=" + siteKey);
    }

    // ===== 🔥 新增：首页顶端功能按钮实现 - 通过适配器调用FongMi_TV功能 =====

    /**
     * 🔥 修复：返回直播界面 - 直接返回到TV应用主模块的主界面
     * 注意：此方法已废弃，直播返回逻辑应直接在UI层使用navController.popBackStack()
     */
    @Deprecated
    public void navigateToLive() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [NAVIGATE_TO_LIVE] 返回直播界面（已废弃方法）");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE", "返回直播界面（已废弃方法）");

        try {
            // 🔥 修复：直播返回逻辑应该在UI层直接使用navController.popBackStack()
            // 这里不再发送EventBus事件，因为直播返回应该直接退出Movie模块
            Log.w(TAG, "[FlowID:" + currentFlowId
                    + "] [NAVIGATE_TO_LIVE_DEPRECATED] 此方法已废弃，请在UI层直接使用navController.popBackStack()");
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE_DEPRECATED",
                    "方法已废弃，应使用navController.popBackStack()");
        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [NAVIGATE_TO_LIVE_ERROR] 返回直播界面失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "NAVIGATE_TO_LIVE_ERROR", "返回直播失败: " + e.getMessage());
        }
    }

    /**
     * 显示观看历史界面 - 通过适配器调用FongMi_TV功能
     */
    public void showHistoryScreen() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_HISTORY] 开始显示观看历史");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_HISTORY", "显示观看历史");

        try {
            // 🔥 按照架构：发送导航事件，UI层负责导航到完整屏幕
            ShowHistoryScreenEvent event = new ShowHistoryScreenEvent(System.currentTimeMillis());
            EventBus.getDefault().post(event);
            Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_HISTORY_SUCCESS] 显示观看历史事件已发送");
        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [SHOW_HISTORY_ERROR] 显示观看历史失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_HISTORY_ERROR", "观看历史失败: " + e.getMessage());
        }
    }

    /**
     * 显示收藏界面 - 通过适配器调用FongMi_TV功能
     */
    public void showFavoritesScreen() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_FAVORITES] 开始显示收藏界面");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_FAVORITES", "显示收藏界面");

        try {
            // 🔥 按照架构：发送导航事件，UI层负责导航到完整屏幕
            ShowFavoritesScreenEvent event = new ShowFavoritesScreenEvent(System.currentTimeMillis());
            EventBus.getDefault().post(event);
            Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_FAVORITES_SUCCESS] 显示收藏界面事件已发送");
        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [SHOW_FAVORITES_ERROR] 显示收藏界面失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_FAVORITES_ERROR", "收藏界面失败: " + e.getMessage());
        }
    }

    /**
     * 🔥 修复：显示站点选择器 - 只显示选择器，不触发配置加载
     */
    public void showSiteSelector() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SITE_SELECTOR] 开始显示站点选择器，不触发配置加载");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR", "显示站点选择器");

        try {
            // 🔥 修复：直接从VodConfig获取站点列表，避免任何可能触发loadConfig的调用
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = VodConfig.get();
            List<top.cywin.onetv.movie.bean.Site> sites = new ArrayList<>();

            // 🔥 关键修复：安全获取站点列表，确保不触发配置加载逻辑
            if (vodConfig != null) {
                try {
                    // 🔥 直接访问内部站点列表，避免触发任何加载逻辑
                    sites = vodConfig.getSites();
                    if (sites == null) {
                        sites = new ArrayList<>();
                    }
                    Log.d(TAG, "[FlowID:" + currentFlowId + "] [SITE_GET_SUCCESS] 安全获取站点列表，数量: " + sites.size());
                } catch (Exception e) {
                    Log.w(TAG, "[FlowID:" + currentFlowId + "] [SITE_GET_WARNING] 获取站点列表异常，使用空列表: " + e.getMessage());
                    sites = new ArrayList<>();
                }
            }

            if (sites.isEmpty()) {
                Log.w(TAG, "[FlowID:" + currentFlowId + "] [SITE_WARNING] 没有可用的站点，可能需要先配置");
                VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SITE_WARNING", "没有可用站点");
                // 🔥 修复：即使没有站点也发送事件，让UI显示空列表
                ShowSiteSelectorEvent event = new ShowSiteSelectorEvent(new ArrayList<>(), System.currentTimeMillis());
                EventBus.getDefault().post(event);
                return;
            }

            // 🔥 通过EventBus发送显示站点选择器事件，包含站点列表
            ShowSiteSelectorEvent event = new ShowSiteSelectorEvent(sites, System.currentTimeMillis());
            EventBus.getDefault().post(event);

            Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SITE_SELECTOR_SUCCESS] 站点选择器显示成功，站点数量: "
                    + sites.size() + "，未触发配置加载");
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR_SUCCESS",
                    "站点选择器显示成功，站点数量: " + sites.size());
        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SITE_SELECTOR_ERROR] 显示站点选择器失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_SITE_SELECTOR_ERROR",
                    "站点选择器失败: " + e.getMessage());
        }
    }

    // 🔥 原版线路选择器逻辑将在此处实现

    /**
     * 🔥 原版FongMi_TV线路切换逻辑 - 100%移植
     * 基于原版FongMi_TV的线路切换机制，确保稳定性和可靠性
     */
    public void switchRoute(String routeUrl) {
        final String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId() != null
                ? VodFlowTracker.INSTANCE.getCurrentFlowId()
                : VodFlowTracker.INSTANCE.generateFlowId();

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [CONFIG_INPUT] 用户输入配置URL: " + routeUrl);
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "CONFIG_INPUT", "用户输入配置URL: " + routeUrl);

        try {
            // 🔥 第一步：异步清理JavaScript资源 - 原版FongMi_TV方式，不阻塞配置加载
            new Thread(() -> clearJavaScriptResources(currentFlowId)).start();

            // 🔥 第二步：按原版FongMi_TV方式创建并保存配置对象
            top.cywin.onetv.movie.bean.Config config = top.cywin.onetv.movie.bean.Config.create(0);
            config.setUrl(routeUrl);
            config.setName("线路切换配置");
            config.setType(0); // VOD配置类型

            // 🔥 原版FongMi_TV关键步骤：保存配置到数据库
            config.save();

            Log.d(TAG, "[FlowID:" + currentFlowId + "] [CONFIG_CREATED] 配置对象创建并保存完成: " + routeUrl);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "CONFIG_CREATED", "配置对象创建并保存完成");

            // 🔥 第三步：立即开始配置下载 - 原版FongMi_TV流程
            Log.d(TAG, "[FlowID:" + currentFlowId + "] [CONFIG_DOWNLOAD] 开始下载配置文件: " + routeUrl);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "CONFIG_DOWNLOAD", "开始下载配置文件: " + routeUrl);
            VodConfig.load(config, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success(String result) {
                    Log.d(TAG, "[FlowID:" + currentFlowId + "] [CONFIG_PARSE] 配置解析成功");
                    VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "CONFIG_PARSE", "配置解析成功");

                    // 🔥 通知UI配置更新成功
                    top.cywin.onetv.movie.event.ConfigUpdateEvent event = new top.cywin.onetv.movie.event.ConfigUpdateEvent(
                            VodConfig.get(), true, null);
                    EventBus.getDefault().post(event);
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "[FlowID:" + currentFlowId + "] [CONFIG_ERROR] 配置加载失败: " + msg);
                    VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "CONFIG_ERROR", "配置加载失败: " + msg);

                    // 🔥 通知UI配置更新失败
                    top.cywin.onetv.movie.event.ConfigUpdateEvent event = new top.cywin.onetv.movie.event.ConfigUpdateEvent(
                            VodConfig.get(), false, msg);
                    EventBus.getDefault().post(event);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [SWITCH_ROUTE_ERROR] 线路切换失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SWITCH_ROUTE_ERROR", "线路切换失败: " + e.getMessage());

            // 🔥 通知UI切换失败
            top.cywin.onetv.movie.event.ConfigUpdateEvent event = new top.cywin.onetv.movie.event.ConfigUpdateEvent(
                    VodConfig.get(), false, "线路切换失败: " + e.getMessage());
            EventBus.getDefault().post(event);
        }
    }

    /**
     * 🔥 清理JavaScript资源 - 原版FongMi_TV关键机制
     * 防止JavaScript内存泄漏和状态残留
     */
    private void clearJavaScriptResources(String flowId) {
        Log.d(TAG, "[FlowID:" + flowId + "] [JS_CLEANUP_START] 开始清理JavaScript资源");
        VodFlowTracker.INSTANCE.logFlowStep(flowId, "JS_CLEANUP_START", "开始清理JavaScript资源");

        try {
            // 🔥 原版FongMi_TV逐个清理JavaScript函数 - 关键步骤
            for (top.cywin.onetv.movie.bean.Site site : VodConfig.get().getSites()) {
                if (site.getType() == 3) { // JavaScript类型站点
                    try {
                        // 🔥 原版FongMi_TV方式：直接调用destroy方法，不创建新的Spider实例
                        Log.d(TAG, "[FlowID:" + flowId + "] [JS_FUNCTION_CALL] 调用JavaScript函数 [" + site.getKey()
                                + "] destroy(无参数)");

                        // 🔥 使用原版FongMi_TV的Spider获取方式
                        com.github.catvod.crawler.Spider spider = top.cywin.onetv.movie.spider.SpiderFactory
                                .create(site);
                        if (spider != null) {
                            long startTime = System.currentTimeMillis();
                            spider.destroy();
                            long endTime = System.currentTimeMillis();

                            Log.d(TAG, "[FlowID:" + flowId + "] [JS_FUNCTION_CALL] JavaScript函数调用成功 [" + site.getKey()
                                    + "] destroy()，耗时: " + (endTime - startTime) + "ms，返回: null");
                            VodFlowTracker.INSTANCE.logFlowStep(flowId, "JS_FUNCTION_CALL",
                                    "JavaScript函数调用成功: " + site.getKey());
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "[FlowID:" + flowId + "] [JS_CLEANUP_ERROR] 清理失败: " + site.getKey(), e);
                        VodFlowTracker.INSTANCE.logFlowStep(flowId, "JS_CLEANUP_ERROR",
                                "清理失败: " + site.getKey() + " - " + e.getMessage());
                        // 🔥 原版FongMi_TV：单个Spider清理失败不影响整体流程
                    }
                }
            }

            // 🔥 原版FongMi_TV：清理BaseLoader - 关键步骤
            try {
                top.cywin.onetv.movie.api.loader.BaseLoader.get().clear();
                Log.d(TAG, "[FlowID:" + flowId + "] [BASE_LOADER_CLEAR] BaseLoader清理完成");
                VodFlowTracker.INSTANCE.logFlowStep(flowId, "BASE_LOADER_CLEAR", "BaseLoader清理完成");
            } catch (Exception e) {
                Log.e(TAG, "[FlowID:" + flowId + "] [BASE_LOADER_CLEAR_ERROR] BaseLoader清理失败: " + e.getMessage(), e);
                VodFlowTracker.INSTANCE.logFlowStep(flowId, "BASE_LOADER_CLEAR_ERROR",
                        "BaseLoader清理失败: " + e.getMessage());
            }

            Log.d(TAG, "[FlowID:" + flowId + "] [JS_CLEANUP_COMPLETE] JavaScript资源清理完成");
            VodFlowTracker.INSTANCE.logFlowStep(flowId, "JS_CLEANUP_COMPLETE", "JavaScript资源清理完成");

        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + flowId + "] [JS_CLEANUP_EXCEPTION] JavaScript资源清理异常: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(flowId, "JS_CLEANUP_EXCEPTION", "JavaScript资源清理异常: " + e.getMessage());
        }
    }

    /**
     * 🔥 获取可用的线路列表
     */
    private List<VodConfigUrl> getAvailableRoutes() {
        List<VodConfigUrl> routes = new ArrayList<>();

        try {
            // 🔥 修复：从FongMi_TV的VodConfig系统获取真实的线路配置
            Log.d("VOD_FLOW", "[ROUTE_LOAD] 开始从VodConfig获取线路配置");

            // 获取当前配置的所有URL（type=0表示VOD配置）
            List<top.cywin.onetv.movie.bean.Config> configs = top.cywin.onetv.movie.bean.Config.findUrls();
            Log.d("VOD_FLOW", "[ROUTE_LOAD] 从数据库获取到 " + configs.size() + " 个配置");

            for (top.cywin.onetv.movie.bean.Config config : configs) {
                if (config.getUrl() != null && !config.getUrl().isEmpty()) {
                    // 创建VodConfigUrl对象
                    String name = config.getName();
                    if (name == null || name.isEmpty()) {
                        name = "线路 " + (routes.size() + 1);
                    }

                    VodConfigUrl route = new VodConfigUrl(name, config.getUrl(), false);
                    routes.add(route);
                    Log.d("VOD_FLOW", "[ROUTE_LOAD] 添加线路: " + name + " -> " + config.getUrl());
                }
            }

            // 🔥 修复：如果没有配置的线路，返回空列表，不添加硬编码默认线路
            if (routes.isEmpty()) {
                Log.w("VOD_FLOW", "[ROUTE_LOAD] 没有找到配置的线路，返回空列表");
            }

            Log.d("VOD_FLOW", "[ROUTE_LOAD] 线路加载完成，共 " + routes.size() + " 条线路");

        } catch (Exception e) {
            Log.e("VOD_FLOW", "[ROUTE_LOAD_ERROR] 从配置加载线路失败", e);

            // 🔥 修复：发生异常时也不使用硬编码默认线路，返回空列表
            routes.clear();
            Log.w("VOD_FLOW", "[ROUTE_LOAD_ERROR] 异常情况下返回空线路列表");
        }

        return routes;
    }

    // 🔥 reloadConfig方法已删除，统一使用switchRoute方法进行线路切换

    /**
     * 显示搜索界面 - 通过适配器调用FongMi_TV功能
     */
    public void showSearchScreen() {
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        if (currentFlowId == null)
            currentFlowId = "UNKNOWN";

        Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SEARCH] 开始显示搜索界面");
        VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_SEARCH", "显示搜索界面");

        try {
            // 🔥 通过EventBus发送显示搜索界面事件
            EventBus.getDefault().post(new ShowSearchScreenEvent());
            Log.d(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SEARCH_SUCCESS] 显示搜索界面事件已发送");
        } catch (Exception e) {
            Log.e(TAG, "[FlowID:" + currentFlowId + "] [SHOW_SEARCH_ERROR] 显示搜索界面失败: " + e.getMessage(), e);
            VodFlowTracker.INSTANCE.logFlowStep(currentFlowId, "SHOW_SEARCH_ERROR", "搜索界面失败: " + e.getMessage());
        }
    }

}
