package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.offset
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieViewModel
import top.cywin.onetv.movie.viewmodel.MovieUiState
import top.cywin.onetv.movie.ui.model.WatchHistory
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.components.MovieCard
import top.cywin.onetv.movie.MovieApp
import android.util.Log

/**
 * OneTV Movie历史记录页面 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieHistoryScreen(
    navController: NavController,
    viewModel: MovieViewModel = viewModel {
        MovieViewModel()
    }
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        // ✅ 通过适配器系统加载历史记录
        viewModel.loadWatchHistory()
    }

    // ✅ UI内容渲染
    HistoryContent(
        uiState = uiState,
        onBack = { navController.popBackStack() },
        onHistoryClick = { history ->
            navController.navigate(top.cywin.onetv.movie.navigation.MovieRoutes.detail(history.vodId, history.siteKey))
        },
        onHistoryPlay = { history ->
            navController.navigate(top.cywin.onetv.movie.navigation.MovieRoutes.player(history.vodId, history.episodeIndex, history.siteKey))
        },
        onHistoryDelete = { history -> viewModel.deleteWatchHistory(history) },
        onClearAllHistory = { viewModel.clearAllWatchHistory() },
        onRefresh = { viewModel.loadWatchHistory() },
        onError = { viewModel.clearError() }
    )
}
@Composable
private fun HistoryContent(
    uiState: MovieUiState,
    onBack: () -> Unit,
    onHistoryClick: (WatchHistory) -> Unit,
    onHistoryPlay: (WatchHistory) -> Unit,
    onHistoryDelete: (WatchHistory) -> Unit,
    onClearAllHistory: () -> Unit,
    onRefresh: () -> Unit,
    onError: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        @OptIn(ExperimentalMaterial3Api::class)
        TopAppBar(
            title = {
                Text(
                    text = "观看历史",
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                // 清空历史按钮
                if (uiState.watchHistories.isNotEmpty()) {
                    IconButton(onClick = onClearAllHistory) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "清空历史"
                        )
                    }
                }
            }
        )

        // 内容区域
        when {
            uiState.isLoadingHistory -> {
                LoadingScreen(message = "正在加载历史记录...")
            }
            uiState.error != null -> {
                val errorMessage = uiState.error ?: "未知错误"
                ErrorScreen(
                    error = errorMessage,
                    onRetry = onRefresh,
                    onBack = onBack
                )
            }
            uiState.watchHistories.isEmpty() -> {
                EmptyHistoryScreen()
            }
            else -> {
                HistoryGridScreen(
                    histories = uiState.watchHistories,
                    onHistoryClick = onHistoryClick,
                    onHistoryPlay = onHistoryPlay,
                    onHistoryDelete = onHistoryDelete
                )
            }
        }
    }
}

@Composable
private fun EmptyHistoryScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.History,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暂无观看历史",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun HistoryGridScreen(
    histories: List<WatchHistory>,
    onHistoryClick: (WatchHistory) -> Unit,
    onHistoryPlay: (WatchHistory) -> Unit,
    onHistoryDelete: (WatchHistory) -> Unit
) {
    Log.d("VOD_FLOW", "[HISTORY_GRID] 渲染观看历史网格，历史记录数: ${histories.size}")

    LazyVerticalGrid(
        columns = GridCells.Fixed(5), // 5列布局，与项目其他地方保持一致
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(histories) { history ->
            HistoryMovieCard(
                history = history,
                onClick = { onHistoryClick(history) },
                onPlay = { onHistoryPlay(history) },
                onDelete = { onHistoryDelete(history) }
            )
        }
    }
}

@Composable
private fun HistoryMovieCard(
    history: WatchHistory,
    onClick: () -> Unit,
    onPlay: () -> Unit,
    onDelete: () -> Unit
) {
    Log.d("VOD_FLOW", "[HISTORY_CARD] 渲染历史记录卡片: ${history.title}, 海报: ${history.vodPic}")

    // ✅ 格式化观看时长显示
    val formatTime = { timeMs: Long ->
        if (timeMs <= 0) "00:00:00"
        else {
            val totalSeconds = timeMs / 1000
            val hours = totalSeconds / 3600
            val minutes = (totalSeconds % 3600) / 60
            val seconds = totalSeconds % 60
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        }
    }

    // 🔥 修复：获取站点的中文名称
    val getSiteName = { siteKey: String ->
        try {
            val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(siteKey)
            site?.name ?: siteKey.replace("onetv_", "").replace("_", " ")
        } catch (e: Exception) {
            Log.w("VOD_FLOW", "获取站点名称失败: $siteKey", e)
            siteKey.replace("onetv_", "").replace("_", " ")
        }
    }

    // ✅ 将WatchHistory转换为MovieItem，包含海报信息（移除重复的观看时长显示）
    val movieItem = MovieItem(
        vodId = history.vodId,
        vodName = history.title,
        vodPic = history.vodPic, // ✅ 使用真实的海报URL
        vodRemarks = "", // 🔥 移除重复的观看时长显示，只在底部显示一次
        vodYear = "",
        vodArea = "",
        vodDirector = "",
        vodActor = "",
        vodContent = "总时长: ${formatTime(history.duration)}",
        siteKey = history.siteKey,
        watchProgress = history.progress,
        lastWatchTime = history.watchTime
    )

    Box(modifier = Modifier.fillMaxWidth()) {
        // 使用项目统一的MovieCard组件
        MovieCard(
            movie = movieItem,
            onClick = onClick,
            showProgress = true,
            progress = history.progress
        )

        // 🔥 修复：将站点标签从下方调整到顶端，使用中文名称
        // 🔥 圆角修复：下左右圆角为方角，上左右圆角为椭圆角，与电影卡片对齐
        Box(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .background(
                    MaterialTheme.colorScheme.secondary.copy(alpha = 0.9f),
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(
                        topStart = 8.dp,    // 上左圆角
                        topEnd = 8.dp,      // 上右圆角
                        bottomStart = 0.dp, // 下左方角
                        bottomEnd = 0.dp    // 下右方角
                    )
                )
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            Text(
                text = getSiteName(history.siteKey),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.Medium
            )
        }

        // 🔥 修复：将观看时间标签调整到电影名字上方（底部中央偏上）
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset(y = (-50).dp) // 向上偏移，放在电影名字上方
                .background(
                    MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(4.dp)
                )
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            Text(
                text = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
                    .format(java.util.Date(history.watchTime)),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )
        }

        // 在卡片右上角添加操作按钮
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(4.dp)
        ) {
            // 继续播放按钮
            IconButton(
                onClick = onPlay,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "继续播放",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
            }

            // 删除按钮
            IconButton(
                onClick = onDelete,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(20.dp)
                )
            }
        }

        // 🔥 修复：将观看至时间标签调整到观看时间的上方（底部中央再向上偏移）
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset(y = (-80).dp) // 比观看时间标签再向上偏移30dp
                .background(
                    MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(4.dp)
                )
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            Text(
                text = "观看至: ${formatTime(history.position)}",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onPrimary,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}
