package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.adapter.ViewModelAdapter
import top.cywin.onetv.movie.event.SearchStartEvent
import top.cywin.onetv.movie.event.SearchResultEvent
import top.cywin.onetv.movie.event.SearchErrorEvent
import top.cywin.onetv.movie.event.ErrorEvent
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.base.MovieViewType

/**
 * OneTV Movie搜索ViewModel - 完整版本
 * 通过适配器系统调用FongMi_TV解析功能，完整的事件驱动架构
 */
class MovieSearchViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_SEARCH_VM"
    }

    // 🔥 原版FongMi_TV直通设计：直接调用SiteViewModel，移除RepositoryAdapter中转层
    private val movieApp = MovieApp.getInstance()
    private val siteViewModel = top.cywin.onetv.movie.model.SiteViewModel()
    private val viewModelAdapter = movieApp.viewModelAdapter

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    init {
        Log.d(TAG, "🏗️ MovieSearchViewModel 初始化")

        // ✅ 注册EventBus监听FongMi_TV事件
        EventBus.getDefault().register(this)

        // ✅ 加载搜索历史
        loadSearchHistory()
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MovieSearchViewModel 清理")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听搜索开始事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchStart(event: SearchStartEvent) {
        // 🔥 关键修复：使用事件中传递的FlowID，确保FlowID连续性
        val searchFlowId = event.sessionId // SearchStartEvent中的sessionId就是FlowID
        top.cywin.onetv.movie.utils.VodFlowTracker.setCurrentFlowId(searchFlowId)
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_START_EVENT", "收到搜索开始事件: keyword=${event.keyword}")

        _uiState.value = _uiState.value.copy(
            isSearching = true,
            currentKeyword = event.keyword,
            error = null
        )
    }

    /**
     * 监听搜索结果事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchResult(event: SearchResultEvent) {
        // 🔥 关键修复：使用事件中传递的FlowID，确保FlowID连续性
        val searchFlowId = event.sessionId.takeIf { it.isNotEmpty() } ?: top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.setCurrentFlowId(searchFlowId)
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_RESULT_EVENT", "收到搜索结果事件: keyword=${event.keyword}, count=${event.results.size}")

        val newMovieItems = event.results.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 🔥 修复：累积搜索结果，而不是替换
        val currentResults = _uiState.value.searchResults.toMutableList()

        // 去重：避免同一个电影被重复添加
        val existingVodIds = currentResults.map { it.vodId }.toSet()
        val uniqueNewItems = newMovieItems.filter { it.vodId !in existingVodIds }

        currentResults.addAll(uniqueNewItems)

        // 🔥 新增：详细记录搜索结果内容
        if (newMovieItems.isNotEmpty()) {
            val movieNames = newMovieItems.take(5).map { it.vodName }.joinToString(", ")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_RESULT_DETAILS", "站点搜索结果 [${event.keyword}]: $movieNames${if (newMovieItems.size > 5) "... 等${newMovieItems.size}部电影" else ""}")
        }

        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_RESULT_ACCUMULATED", "累积搜索结果: 新增${uniqueNewItems.size}部，总计${currentResults.size}部电影")

        _uiState.value = _uiState.value.copy(
            isSearching = false,
            searchResults = currentResults,
            hasMore = event.hasMore,
            currentPage = event.page,
            totalCount = currentResults.size,
            error = null
        )

        // 🔥 修复：搜索历史记录逻辑
        // 在这个阶段不保存搜索历史，搜索历史应该在用户点击具体电影名字时保存
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_HISTORY_SKIP", "跳过搜索历史保存，等待用户点击具体电影名字")

        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(searchFlowId, "SEARCH_RESULT_PROCESSED", "搜索结果处理完成: 总计${currentResults.size}条电影数据")
    }

    /**
     * 监听搜索错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchError(event: SearchErrorEvent) {
        Log.e(TAG, "📡 收到搜索错误事件: keyword=${event.keyword}, error=${event.error}")

        _uiState.value = _uiState.value.copy(
            isSearching = false,
            error = event.error
        )
    }

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        Log.e(TAG, "📡 收到错误事件: ${event.msg}")

        _uiState.value = _uiState.value.copy(
            isSearching = false,
            error = event.msg
        )
    }

    /**
     * 🔥 监听首页内容事件 - 用于提取热门数据
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onHomeContent(event: top.cywin.onetv.movie.event.HomeContentEvent) {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOME_CONTENT_EVENT", "收到首页内容事件")

        try {
            // 🔥 按照原版FongMi_TV架构：从首页内容中提取热门数据
            val hotMovies = mutableListOf<String>()
            val hotTvShows = mutableListOf<String>()

            // 🔥 从推荐内容中提取热门数据
            event.recommendVods.take(10).forEach { vod ->
                vod.vodName?.let { name ->
                    when {
                        vod.typeName?.contains("电影") == true -> hotMovies.add(name)
                        vod.typeName?.contains("电视") == true || vod.typeName?.contains("剧") == true -> hotTvShows.add(name)
                        else -> hotMovies.add(name) // 默认归类为电影
                    }
                }
            }

            // 🔥 从分类中提取更多热门数据
            event.categories.forEach { category ->
                when {
                    category.typeName?.contains("电影") == true && hotMovies.size < 20 -> {
                        // 电影分类，但我们需要从实际的内容中获取，这里先跳过
                    }
                    (category.typeName?.contains("电视") == true || category.typeName?.contains("剧") == true) && hotTvShows.size < 20 -> {
                        // 电视剧分类，但我们需要从实际的内容中获取，这里先跳过
                    }
                }
            }

            _uiState.value = _uiState.value.copy(
                hotMovies = hotMovies,
                hotTvShows = hotTvShows,
                isLoadingHotData = false
            )

            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_DATA_EXTRACTED", "热门数据提取完成: 电影${hotMovies.size}部, 电视剧${hotTvShows.size}部")

        } catch (e: Exception) {
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_DATA_EXTRACT_ERROR", "处理首页内容事件失败: ${e.message}")
            _uiState.value = _uiState.value.copy(
                isLoadingHotData = false,
                error = "提取热门数据失败: ${e.message}"
            )
        }
    }

    // ===== 公共方法 =====

    /**
     * 搜索内容
     */
    fun search(keyword: String, siteKey: String = "") {
        val trimmedKeyword = keyword.trim()
        if (trimmedKeyword.isEmpty()) {
            _uiState.value = _uiState.value.copy(error = "请输入搜索关键词")
            return
        }

        viewModelScope.launch {
            try {
                // 🔥 关键修复：使用现有FlowID或生成新的搜索FlowID
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId().takeIf { it != "UNKNOWN" }
                    ?: top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.setCurrentFlowId(currentFlowId)
                top.cywin.onetv.movie.utils.VodFlowTracker.setGlobalFlowId(currentFlowId)
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_START", "开始搜索: keyword=$trimmedKeyword, siteKey=$siteKey")

                // 🔥 关键修复：按原版FongMi_TV设计，立即导航到搜索结果页面，跳过"正在搜索"页面
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_IMMEDIATE_NAVIGATE", "立即导航到搜索结果页面，跳过正在搜索页面")
                EventBus.getDefault().post(top.cywin.onetv.movie.event.NavigateToSearchResultsEvent(trimmedKeyword))

                _uiState.value = _uiState.value.copy(
                    isSearching = true,
                    currentKeyword = trimmedKeyword,
                    searchResults = emptyList(),
                    currentPage = 1,
                    hasMore = false,
                    totalCount = 0,
                    error = null
                )

                // 🔥 原版FongMi_TV直接调用方式 - 不通过适配器，直接调用SiteViewModel
                siteViewModel.searchContent(trimmedKeyword, false)

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_REQUEST_SENT", "搜索请求已发送")

            } catch (e: Exception) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_ERROR", "搜索失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isSearching = false,
                    error = "搜索失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新搜索关键词
     */
    fun updateKeyword(keyword: String) {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "UPDATE_KEYWORD", "更新搜索关键词: $keyword")

        _uiState.value = _uiState.value.copy(searchKeyword = keyword)

        // 🔥 修复：只要搜索框有数据输入，立即展示相关电影
        if (keyword.isNotEmpty()) {
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "REAL_TIME_SEARCH_START", "开始实时搜索: $keyword")
            performRealTimeSearch(keyword)
        } else {
            // 清空搜索结果，显示热门内容
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_SEARCH_RESULTS", "清空搜索结果，显示热门内容")
            _uiState.value = _uiState.value.copy(
                searchResults = emptyList(),
                searchSuggestions = emptyList()
            )
        }
    }

    /**
     * 🔥 新增：实时搜索功能 - 用户输入字符后立即搜索
     */
    private fun performRealTimeSearch(keyword: String) {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "REAL_TIME_SEARCH", "执行实时搜索: $keyword")

        viewModelScope.launch {
            try {
                // 🔥 修复：开始新搜索时清空之前的搜索结果
                _uiState.value = _uiState.value.copy(
                    isSearching = true,
                    error = null,
                    searchResults = emptyList() // 清空之前的搜索结果
                )

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_RESULTS_CLEARED", "清空之前的搜索结果，开始新搜索")

                // 🔥 原版FongMi_TV直接调用方式 - 不通过适配器，直接调用SiteViewModel
                siteViewModel.searchContent(keyword, false)

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "REAL_TIME_SEARCH_SENT", "实时搜索请求已发送")

            } catch (e: Exception) {
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "REAL_TIME_SEARCH_ERROR", "实时搜索失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isSearching = false,
                    error = "实时搜索失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除搜索内容
     */
    fun clearSearch() {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_SEARCH", "清除搜索内容")

        _uiState.value = _uiState.value.copy(
            searchKeyword = "",
            searchResults = emptyList(),
            currentKeyword = "",
            searchSuggestions = emptyList(),
            isSearching = false,
            error = null
        )
    }

    /**
     * 从历史记录搜索
     */
    fun searchFromHistory(keyword: String) {
        _uiState.value = _uiState.value.copy(searchKeyword = keyword)
        search(keyword)
    }



    /**
     * 删除单个搜索历史
     */
    fun removeSearchHistory(keyword: String) {
        viewModelScope.launch {
            try {
                val updatedHistory = _uiState.value.searchHistory.filter { it != keyword }
                _uiState.value = _uiState.value.copy(searchHistory = updatedHistory)

                // 更新本地存储
                saveLocalSearchHistory(updatedHistory)

            } catch (e: Exception) {
                Log.e(TAG, "删除搜索历史失败", e)
            }
        }
    }

    /**
     * 显示搜索建议
     */
    fun showSearchSuggestions() {
        _uiState.value = _uiState.value.copy(showSuggestions = true)
    }

    /**
     * 隐藏搜索建议
     */
    fun hideSearchSuggestions() {
        _uiState.value = _uiState.value.copy(showSuggestions = false)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 🔥 加载热门搜索数据 - 完全按照原版FongMi_TV架构实现
     */
    fun loadHotSearchData() {
        viewModelScope.launch {
            try {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_DATA_LOAD_START", "开始加载热门搜索数据")

                _uiState.value = _uiState.value.copy(isLoadingHotData = true)

                // 🔥 按照原版FongMi_TV架构：通过RepositoryAdapter获取首页内容
                movieApp.repositoryAdapter.getHomeContent()

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_DATA_REQUEST_SENT", "热门搜索数据请求已发送，等待HomeContentEvent")

            } catch (e: Exception) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_DATA_LOAD_ERROR", "加载热门搜索数据失败: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoadingHotData = false,
                    error = "加载热门数据失败: ${e.message}"
                )
            }
        }
    }

    // ===== 私有方法 =====

    /**
     * 加载搜索历史
     */
    private fun loadSearchHistory() {
        viewModelScope.launch {
            try {
                val history = getLocalSearchHistory()
                _uiState.value = _uiState.value.copy(searchHistory = history)
            } catch (e: Exception) {
                Log.e(TAG, "加载搜索历史失败", e)
            }
        }
    }

    /**
     * 🔥 修复：保存用户最终选择的电影名称到搜索历史
     * 按照用户要求：搜索历史要记载的是用户最终在热门搜索区的选择，不是搜索框中的字符
     */
    fun saveMovieNameToHistory(movieName: String) {
        viewModelScope.launch {
            try {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_HISTORY", "保存电影名称到历史: $movieName")

                val currentHistory = _uiState.value.searchHistory.toMutableList()

                // 移除重复项
                currentHistory.remove(movieName)

                // 添加到开头
                currentHistory.add(0, movieName)

                // 限制历史记录数量
                if (currentHistory.size > 20) {
                    currentHistory.removeAt(currentHistory.size - 1)
                }

                _uiState.value = _uiState.value.copy(searchHistory = currentHistory)

                // 🔥 按照原版FongMi_TV架构：保存到History数据库
                saveMovieNameToDatabase(movieName)

                Log.d(TAG, "✅ [搜索历史修复] 成功保存电影名称到历史: $movieName")

            } catch (e: Exception) {
                Log.e(TAG, "❌ [搜索历史修复] 保存电影名称到历史失败", e)
            }
        }
    }

    /**
     * 保存搜索历史（原有方法，保持兼容性）
     */
    private fun saveSearchHistory(keyword: String) {
        viewModelScope.launch {
            try {
                val currentHistory = _uiState.value.searchHistory.toMutableList()

                // 移除重复项
                currentHistory.remove(keyword)

                // 添加到开头
                currentHistory.add(0, keyword)

                // 限制历史记录数量
                if (currentHistory.size > 20) {
                    currentHistory.removeAt(currentHistory.size - 1)
                }

                _uiState.value = _uiState.value.copy(searchHistory = currentHistory)

                // 保存到本地存储
                saveLocalSearchHistory(currentHistory)

            } catch (e: Exception) {
                Log.e(TAG, "保存搜索历史失败", e)
            }
        }
    }

    /**
     * 更新搜索建议
     */
    private fun updateSearchSuggestions(keyword: String) {
        viewModelScope.launch {
            try {
                // 从搜索历史中筛选建议
                val suggestions = _uiState.value.searchHistory
                    .filter { it.contains(keyword, ignoreCase = true) }
                    .take(5)

                _uiState.value = _uiState.value.copy(searchSuggestions = suggestions)

            } catch (e: Exception) {
                Log.e(TAG, "更新搜索建议失败", e)
            }
        }
    }

    /**
     * 🔥 获取本地搜索历史 - 按照原版FongMi_TV架构使用数据库存储
     */
    private suspend fun getLocalSearchHistory(): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "LOAD_SEARCH_HISTORY_START", "开始从数据库读取搜索历史")

                // 🔥 按照原版FongMi_TV架构：使用History数据库存储搜索历史
                val historyList = top.cywin.onetv.movie.database.AppDatabase.get()
                    .getHistoryDao()
                    .findAll()
                    .take(20) // 限制数量
                    .map { it.vodName ?: "" }
                    .filter { it.isNotEmpty() }
                    .distinct()

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "LOAD_SEARCH_HISTORY_SUCCESS", "从数据库读取搜索历史成功: ${historyList.size}条")
                historyList
            } catch (e: Exception) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "LOAD_SEARCH_HISTORY_ERROR", "读取搜索历史失败: ${e.message}")
                emptyList()
            }
        }
    }

    /**
     * 🔥 修复：保存电影名称到数据库 - 按照原版FongMi_TV架构
     */
    private suspend fun saveMovieNameToDatabase(movieName: String) {
        withContext(Dispatchers.IO) {
            try {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_DB_START", "开始保存电影名称到数据库: $movieName")

                // 🔥 按照原版FongMi_TV架构：创建History记录
                val history = top.cywin.onetv.movie.bean.History().apply {
                    setKey("search_${System.currentTimeMillis()}")
                    setVodName(movieName)
                    setCid(top.cywin.onetv.movie.api.config.VodConfig.getCid())
                    setCreateTime(System.currentTimeMillis())
                }

                // 保存到数据库
                top.cywin.onetv.movie.database.AppDatabase.get()
                    .getHistoryDao()
                    .insertOrUpdate(history)

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_DB_SUCCESS", "成功保存电影名称到数据库: $movieName")
                Log.d(TAG, "✅ [搜索历史修复] 成功保存电影名称到数据库: $movieName")

            } catch (e: Exception) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_DB_ERROR", "保存电影名称到数据库失败: ${e.message}")
                Log.e(TAG, "❌ [搜索历史修复] 保存电影名称到数据库失败", e)
            }
        }
    }

    /**
     * 🔥 保存本地搜索历史 - 按照原版FongMi_TV架构使用数据库存储
     */
    private suspend fun saveLocalSearchHistory(history: List<String>) {
        withContext(Dispatchers.IO) {
            try {
                // 🔥 按照原版FongMi_TV架构：搜索历史通过实际搜索行为自动保存到History表
                // 这里不需要手动保存，因为每次搜索都会自动记录到数据库
                Log.d(TAG, "✅ [FongMi_TV兼容] 搜索历史将通过实际搜索行为自动保存")
            } catch (e: Exception) {
                Log.e(TAG, "❌ [FongMi_TV兼容] 保存搜索历史失败", e)
            }
        }
    }

    /**
     * 🔥 清空搜索历史 - 用户点击清空按钮时调用
     */
    fun clearSearchHistory() {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_SEARCH_HISTORY", "清空搜索历史")

        viewModelScope.launch {
            try {
                // 清空UI状态中的搜索历史
                _uiState.value = _uiState.value.copy(searchHistory = emptyList())

                // 清空数据库中的搜索历史
                clearLocalSearchHistory()

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_SEARCH_HISTORY_SUCCESS", "搜索历史清空成功")
            } catch (e: Exception) {
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "CLEAR_SEARCH_HISTORY_ERROR", "搜索历史清空失败: ${e.message}")
            }
        }
    }

    /**
     * 🔥 删除单个搜索历史记录
     */
    fun deleteSearchHistoryItem(keyword: String) {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "DELETE_SEARCH_HISTORY_ITEM", "删除搜索历史项: $keyword")

        viewModelScope.launch {
            try {
                // 从UI状态中移除该项
                val updatedHistory = _uiState.value.searchHistory.filter { it != keyword }
                _uiState.value = _uiState.value.copy(searchHistory = updatedHistory)

                // 更新本地存储
                saveLocalSearchHistory(updatedHistory)

                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "DELETE_SEARCH_HISTORY_ITEM_SUCCESS", "搜索历史项删除成功: $keyword")
            } catch (e: Exception) {
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "DELETE_SEARCH_HISTORY_ITEM_ERROR", "搜索历史项删除失败: ${e.message}")
            }
        }
    }

    /**
     * 🔥 新增：用户点击电影时保存搜索历史
     * 这个方法在用户点击具体电影名字时调用，保存的是电影名字而不是搜索关键词
     */
    fun saveMovieClickHistory(movieName: String) {
        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_CLICK_HISTORY", "保存电影点击历史: $movieName")

        if (movieName.isNotEmpty()) {
            saveSearchHistory(movieName)
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SAVE_MOVIE_CLICK_HISTORY_SUCCESS", "成功保存电影点击历史: $movieName")
        }
    }

    /**
     * 🔥 清除本地搜索历史 - 按照原版FongMi_TV架构清除History数据库
     */
    private suspend fun clearLocalSearchHistory() {
        withContext(Dispatchers.IO) {
            try {
                // 🔥 按照原版FongMi_TV架构：清除History数据库中的所有记录
                top.cywin.onetv.movie.database.AppDatabase.get()
                    .getHistoryDao()
                    .delete()

                Log.d(TAG, "✅ [FongMi_TV兼容] 搜索历史已清除")
            } catch (e: Exception) {
                Log.e(TAG, "❌ [FongMi_TV兼容] 清除搜索历史失败", e)
            }
        }
    }
}

/**
 * 搜索UI状态数据类
 */
data class SearchUiState(
    // 基础状态
    val isSearching: Boolean = false,
    val error: String? = null,

    // 搜索数据
    val searchKeyword: String = "",
    val currentKeyword: String = "",
    val searchResults: List<MovieItem> = emptyList(),
    val currentPage: Int = 1,
    val hasMore: Boolean = false,
    val totalCount: Int = 0,

    // 搜索历史和建议
    val searchHistory: List<String> = emptyList(),
    val searchSuggestions: List<String> = emptyList(),
    val showSuggestions: Boolean = false,

    // 🔥 热门数据 - 根据接口文件中实际数据来展示
    val hotMovies: List<String> = emptyList(),
    val hotTvShows: List<String> = emptyList(),
    val isLoadingHotData: Boolean = false,

    // UI控制
    val viewType: MovieViewType = MovieViewType.RECT,
    val showSearchHistory: Boolean = true
)
