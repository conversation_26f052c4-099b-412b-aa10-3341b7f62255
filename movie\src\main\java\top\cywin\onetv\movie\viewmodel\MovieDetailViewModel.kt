package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import android.util.Log

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.*
import top.cywin.onetv.movie.event.MovieIdTransformEvent
import top.cywin.onetv.movie.ui.model.*
import top.cywin.onetv.movie.adapter.ViewModelAdapter
import top.cywin.onetv.movie.utils.VodFlowTracker
import top.cywin.onetv.movie.api.config.VodConfig

/**
 * 详情页UI状态数据类 - 完整版本
 */
data class DetailUiState(
    // 基础状态
    val isLoading: Boolean = false,
    val error: String? = null,
    val vodId: String = "",
    val siteKey: String = "",

    // 影片信息
    val movie: MovieItem? = null,

    // 播放相关
    val playFlags: List<PlayFlag> = emptyList(),
    val currentFlag: PlayFlag? = null,
    val episodes: List<Episode> = emptyList(),
    val currentEpisode: Episode? = null,
    val isParsingPlayUrl: Boolean = false,
    val shouldAutoPlay: Boolean = false,  // 🔥 新增：是否应该自动播放（跳转到全屏播放器）
    val shouldEmbeddedAutoPlay: Boolean = false,  // 🔥 新增：是否应该在内嵌播放器中自动播放

    // 用户状态
    val isFavorite: Boolean = false,
    val watchHistory: WatchHistory? = null,

    // UI控制
    val showFlagSelector: Boolean = false,
    val showEpisodeSelector: Boolean = false,
    val showMoreInfo: Boolean = false,

    // 相关推荐
    val relatedMovies: List<MovieItem> = emptyList(),
    val isLoadingRelated: Boolean = false
)

/**
 * OneTV Movie详情页ViewModel - 完整版本
 * 处理影片详情、播放源、剧集等完整功能
 */
class MovieDetailViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_DETAIL_VM"
    }

    // ✅ 通过MovieApp访问适配器系统
    private val movieApp = MovieApp.getInstance()
    private val repositoryAdapter = movieApp.repositoryAdapter
    private val viewModelAdapter = movieApp.viewModelAdapter

    private val _uiState = MutableStateFlow(DetailUiState())
    val uiState: StateFlow<DetailUiState> = _uiState.asStateFlow()

    // 🔥 FlowID用于全流程跟踪
    private lateinit var currentFlowId: String

    // 🔥 海报修复：保存传递的海报URL
    private var passedVodPic: String? = null

    // 🔥 关键修复：标记当前ViewModel是否为活跃状态，防止EventBus事件风暴
    private var isActive = false
    private var currentVodId: String? = null
    private var currentSiteKey: String? = null

    // 🔥 关键修复：防止重复播放地址触发循环
    private var lastProcessedPlayUrl: String? = null

    init {
        Log.d(TAG, "🏗️ MovieDetailViewModel 初始化")

        // ✅ 注册EventBus监听FongMi_TV事件
        EventBus.getDefault().register(this)
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MovieDetailViewModel 清理")

        // 🔥 关键修复：重置活跃状态
        isActive = false
        currentVodId = null
        currentSiteKey = null
        Log.d(TAG, "🔥 [EventBus修复] 重置ViewModel活跃状态")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听电影ID转换事件 - 实现原版VideoActivity的ID转换机制
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMovieIdTransform(event: MovieIdTransformEvent) {
        Log.d(TAG, "📡 [FongMi_TV兼容] 收到ID转换事件: success=${event.isSuccess}")
        Log.d(TAG, "📊 [FongMi_TV兼容] 转换结果: ${event.movieName} -> vodId: ${event.realVodId}, siteKey: ${event.siteKey}")

        if (event.isSuccess && event.realVodId != null) {
            // ID转换成功，使用真实ID加载详情
            Log.d(TAG, "✅ [FongMi_TV兼容] ID转换成功，开始加载详情")
            loadMovieDetail(event.realVodId, event.siteKey)
        } else {
            // ID转换失败
            Log.e(TAG, "❌ [FongMi_TV兼容] ID转换失败: ${event.error}")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = event.error ?: "ID转换失败"
            )
        }
    }

    /**
     * 监听内容详情事件
     * 🔥 关键修复：只有活跃的ViewModel才处理事件，防止EventBus事件风暴
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onContentDetail(event: ContentDetailEvent) {
        // 🔥 关键修复：只有活跃的ViewModel且匹配当前请求的才处理事件
        if (!isActive) {
            Log.d(TAG, "🚫 [EventBus修复] ViewModel非活跃状态，忽略ContentDetailEvent")
            return
        }

        // 检查事件是否匹配当前请求
        val eventVodId = event.vod?.vodId
        val eventSiteKey = event.vod?.siteKey
        if (eventVodId != currentVodId || eventSiteKey != currentSiteKey) {
            Log.d(TAG, "🚫 [EventBus修复] 事件不匹配当前请求，忽略: eventVodId=$eventVodId, currentVodId=$currentVodId")
            return
        }

        Log.d(TAG, "📡 [EventBus修复] 活跃ViewModel收到匹配的内容详情事件: success=${event.success}")

        try {
            if (event.vod != null && event.success) {
                // 🔥 处理成功后立即设置为非活跃，防止重复处理
                isActive = false
                handleContentDetailSuccess(event.vod)
            } else {
                Log.w(TAG, "⚠️ [详情事件修复] 详情获取失败: ${event.errorMessage}")
                isActive = false
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = event.errorMessage ?: "获取详情失败"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "💥 详情处理失败", e)
            isActive = false
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "详情处理异常: ${e.message}"
            )
        }
    }

    /**
     * 监听收藏更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFavoriteUpdate(event: FavoriteUpdateEvent) {
        Log.d(TAG, "📡 收到收藏更新事件: vodId=${event.vodId}, favorite=${event.isFavorite}")

        if (event.isSuccess) {
            _uiState.value = _uiState.value.copy(
                isFavorite = event.isFavorite
            )
        }
    }

    /**
     * 监听历史更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onHistoryUpdate(event: HistoryUpdateEvent) {
        Log.d(TAG, "📡 收到历史更新事件: vodId=${event.vodId}")

        if (event.isSuccess) {
            val watchHistory = WatchHistory(
                vodId = event.vodId,
                vodName = _uiState.value.movie?.vodName ?: "",
                siteKey = _uiState.value.movie?.siteKey ?: "",
                episodeName = _uiState.value.currentEpisode?.name ?: "",
                position = event.position,
                duration = event.duration,
                watchTime = System.currentTimeMillis(),
                isCompleted = event.position >= event.duration * 0.9
            )

            _uiState.value = _uiState.value.copy(
                watchHistory = watchHistory
            )
        }
    }

    /**
     * 监听播放地址解析事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayUrlParse(event: PlayUrlParseEvent) {
        // 🔥 关键修复：添加详细的事件接收日志
        Log.d("VOD_FLOW", "🎯 [DETAIL_EVENT_RECEIVED] MovieDetailViewModel收到PlayUrlParseEvent")
        Log.d("VOD_FLOW", "🎯 [DETAIL_EVENT_INFO] 事件详情: playUrl=${event.playUrl}, flag=${event.flag}, flowId=${event.flowId}")

        val eventFlowId = event.flowId ?: (if (::currentFlowId.isInitialized) currentFlowId else null)

        if (eventFlowId != null) {
            VodFlowTracker.logFlowStep(eventFlowId, "PLAY_URL_PARSE_EVENT", "收到播放地址解析事件: ${event.playUrl}")
        }
        Log.d(TAG, "📡 收到播放地址解析事件, FlowID: $eventFlowId")
        Log.d(TAG, "🎬 解析结果: url=${event.playUrl}")
        Log.d(TAG, "🎬 播放线路: flag=${event.flag}")

        if (!event.playUrl.isNullOrEmpty()) {
            // 🔥 关键修复：防止重复播放地址触发循环
            if (event.playUrl == lastProcessedPlayUrl) {
                Log.d("VOD_FLOW", "🎯 [DETAIL_DUPLICATE_SKIP] 跳过重复播放地址: ${event.playUrl}")
                return
            }

            // 🔥 更新当前剧集的播放地址
            val currentState = _uiState.value
            Log.d("VOD_FLOW", "🎯 [DETAIL_STATE_CHECK] 当前状态: currentEpisode=${currentState.currentEpisode?.name}")

            if (currentState.currentEpisode != null) {
                // 🔥 记录已处理的播放地址，防止重复处理
                lastProcessedPlayUrl = event.playUrl

                val updatedEpisode = currentState.currentEpisode.copy(
                    playUrl = event.playUrl
                )

                _uiState.value = currentState.copy(
                    currentEpisode = updatedEpisode,
                    isParsingPlayUrl = false,
                    shouldEmbeddedAutoPlay = true  // 🔥 修复：标记应该在内嵌播放器中自动播放
                )

                if (::currentFlowId.isInitialized) {
                    VodFlowTracker.logFlowSuccess(currentFlowId, "PLAY_URL_UPDATE", "播放地址更新成功: ${event.playUrl}")
                }
                Log.d("VOD_FLOW", "🎯 [DETAIL_AUTO_PLAY] 设置shouldEmbeddedAutoPlay=true，准备在内嵌播放器中自动播放")
                Log.d(TAG, "✅ 播放地址更新成功，准备在内嵌播放器中自动播放: ${event.playUrl}")
            } else {
                Log.w("VOD_FLOW", "🎯 [DETAIL_STATE_ERROR] currentEpisode为空，无法更新播放地址")
            }
        } else {
            if (::currentFlowId.isInitialized) {
                VodFlowTracker.logFlowError(currentFlowId, "PLAY_URL_PARSE", "播放地址为空")
            }
            Log.w(TAG, "⚠️ 播放地址为空")
            _uiState.value = _uiState.value.copy(
                isParsingPlayUrl = false,
                error = "播放地址解析失败"
            )
        }
    }

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        Log.e(TAG, "📡 收到错误事件: ${event.msg}")

        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = event.msg
        )
    }

    // ===== 公共方法 =====

    /**
     * 加载影片详情 - 标准方法
     * 🔥 关键修复：设置活跃状态，防止EventBus事件风暴
     * 🔥 海报修复：添加海报URL参数，对标原版FongMi_TV
     */
    fun loadMovieDetail(vodId: String, siteKey: String = "", flowId: String? = null, vodPic: String? = null) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "🎬 [FongMi_TV兼容] 开始加载影片详情: vodId=$vodId, siteKey=$siteKey, flowId=$flowId, vodPic=$vodPic")
                Log.d(TAG, "🎯 [电影ID跟踪] MovieDetailViewModel.loadMovieDetail被调用")

                // 🔥 FlowID修复：确保FlowID不为null
                val actualFlowId = flowId ?: VodFlowTracker.getCurrentFlowId() ?: "VM_${System.currentTimeMillis()}"

                // 🔥 海报修复：保存传递的海报URL，对标原版FongMi_TV的checkHistory逻辑
                passedVodPic = vodPic
                if (!vodPic.isNullOrEmpty()) {
                    Log.d("VOD_FLOW", "[FlowID:$actualFlowId] [DETAIL_PIC_PASSED] 接收到搜索结果海报URL: $vodPic")
                } else {
                    Log.w("VOD_FLOW", "[FlowID:$actualFlowId] [DETAIL_PIC_EMPTY] 没有传入海报URL")
                }

                // 🔥 关键修复：设置FlowID，确保连续性
                currentFlowId = actualFlowId
                VodFlowTracker.setCurrentFlowId(actualFlowId)
                Log.d("VOD_FLOW", "[FlowID:$actualFlowId] [DETAIL_VM_FLOWID_SET] ViewModel设置FlowID: $actualFlowId")
                Log.d("VOD_FLOW", "[FlowID:$actualFlowId] [DETAIL_VM_START] MovieDetailViewModel开始加载影片详情")

                // 🔥 关键修复：设置当前ViewModel为活跃状态
                isActive = true
                currentVodId = vodId
                currentSiteKey = siteKey
                Log.d(TAG, "🔥 [EventBus修复] 设置ViewModel为活跃状态: vodId=$vodId, siteKey=$siteKey")

                // ✅ 数据有效性检查（不过滤，只是记录警告）
                if (vodId.isBlank()) {
                    Log.w(TAG, "⚠️ [FongMi_TV兼容] 电影ID为空，但仍尝试加载")
                }

                if (vodId == "no_data" || vodId.contains("无数据")) {
                    Log.w(TAG, "⚠️ [FongMi_TV兼容] 检测到可能的无效数据，但仍尝试加载: $vodId")
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null,
                    vodId = vodId,
                    siteKey = siteKey
                )

                // ✅ 通过适配器获取影片详情（添加超时保护）
                withTimeout(30000) { // 30秒超时
                    Log.d(TAG, "🔄 [FongMi_TV兼容] 调用RepositoryAdapter.getContentDetail")
                    Log.d(TAG, "🎯 [电影ID跟踪] 调用getContentDetail: vodId=$vodId, siteKey=$siteKey")

                    // 🔥 关键修复：设置FlowID上下文，确保SiteViewModel能获取到正确的FlowID
                    if (::currentFlowId.isInitialized) {
                        VodFlowTracker.setCurrentFlowId(currentFlowId)
                        Log.d(TAG, "🔥 [FlowID修复] 设置详情页面FlowID上下文: $currentFlowId")
                    }

                    repositoryAdapter.getContentDetail(vodId, siteKey)
                }

                // ✅ 同时检查收藏状态和观看历史
                // 🔥 海报修复：传递海报URL到历史检查方法
                checkFavoriteAndHistory(vodId, siteKey, passedVodPic)

            } catch (e: TimeoutCancellationException) {
                Log.e(TAG, "⏰ 影片详情加载超时: vodId=$vodId")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载超时，请重试"
                )
            } catch (e: Exception) {
                Log.e(TAG, "💥 影片详情加载失败: vodId=$vodId", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "详情加载失败，但不影响应用运行"
                )
            }
        }
    }

    /**
     * 切换收藏状态
     */
    fun toggleFavorite() {
        val currentState = _uiState.value
        val movie = currentState.movie ?: return

        viewModelScope.launch {
            try {
                val newFavoriteState = !currentState.isFavorite

                // ✅ 乐观更新UI
                _uiState.value = _uiState.value.copy(isFavorite = newFavoriteState)

                // ✅ 通过适配器更新收藏状态
                if (newFavoriteState) {
                    // 需要转换为FongMi_TV的Vod对象
                    val fongmiVod = convertToFongMiVod(movie)
                    repositoryAdapter.addToFavorites(fongmiVod)
                } else {
                    repositoryAdapter.removeFromFavorites(movie.vodId, movie.siteKey)
                }

                Log.d(TAG, "✅ 收藏状态更新: ${if (newFavoriteState) "已收藏" else "已取消收藏"}")

            } catch (e: Exception) {
                Log.e(TAG, "💥 收藏操作失败", e)
                // 回滚UI状态
                _uiState.value = _uiState.value.copy(isFavorite = currentState.isFavorite)
            }
        }
    }

    /**
     * 选择播放线路
     */
    fun selectFlag(flag: PlayFlag) {
        Log.d(TAG, "🔄 选择播放线路: ${flag.flag}")

        val episodes = ViewModelAdapter.convertVodEpisodes(flag.urls)
        val defaultEpisode = episodes.firstOrNull()

        _uiState.value = _uiState.value.copy(
            currentFlag = flag,
            episodes = episodes,
            currentEpisode = defaultEpisode,
            showFlagSelector = false
        )
    }

    /**
     * 选择剧集
     */
    fun selectEpisode(episode: Episode) {
        Log.d(TAG, "🔄 选择剧集: ${episode.name}")

        // 🔥 关键修复：切换剧集时重置播放地址标记，允许新剧集播放
        lastProcessedPlayUrl = null

        _uiState.value = _uiState.value.copy(
            currentEpisode = episode,
            showEpisodeSelector = false
        )

        val currentState = _uiState.value
        val currentFlag = currentState.currentFlag
        if (currentFlag != null) {
            Log.d(TAG, " [集数切换] 开始解析新剧集播放地址: ${episode.name}")
            parsePlayUrl(episode, currentFlag)
        } else {
            Log.w(TAG, "⚠️ [集数切换] 当前播放线路为空，无法解析播放地址")
        }
    }

    /**
     * 解析播放地址 - 用于集数切换
     */
    private fun parsePlayUrl(episode: Episode, flag: PlayFlag) {
        Log.d(TAG, "🔄 [集数切换] 开始解析播放地址: episode=${episode.name}, flag=${flag.flag}")

        // 设置解析状态
        _uiState.value = _uiState.value.copy(
            isParsingPlayUrl = true,
            shouldEmbeddedAutoPlay = false
        )

        try {
            // 🔥 关键修复：确保FlowID连续性
            val flowIdToUse = if (::currentFlowId.isInitialized) {
                Log.d(TAG, "🔥 [集数切换FlowID] 使用已有FlowID: $currentFlowId")
                currentFlowId
            } else {
                // 🔥 尝试从全局FlowID获取
                val globalFlowId = VodFlowTracker.getGlobalFlowId()
                if (globalFlowId != null) {
                    Log.d(TAG, "🔥 [集数切换FlowID] 从全局FlowID获取: $globalFlowId")
                    currentFlowId = globalFlowId
                    globalFlowId
                } else {
                    Log.w(TAG, "⚠️ [集数切换FlowID] 全局FlowID为空，生成新FlowID")
                    val newFlowId = VodFlowTracker.generateFlowId()
                    currentFlowId = newFlowId
                    VodFlowTracker.setGlobalFlowId(newFlowId)
                    newFlowId
                }
            }

            // 设置FlowID上下文
            VodFlowTracker.setCurrentFlowId(flowIdToUse)
            VodFlowTracker.setGlobalFlowId(flowIdToUse)
            VodFlowTracker.logFlowStep(flowIdToUse, "EPISODE_SWITCH_PARSE", "集数切换解析播放地址: ${episode.name}")

            // 获取当前电影信息
            val currentMovie = _uiState.value.movie
            if (currentMovie != null) {
                // 调用适配器解析播放地址
                repositoryAdapter.parsePlayUrl(episode.url, currentMovie.siteKey, flag.flag)
                Log.d(TAG, "✅ [FlowID:$flowIdToUse] 集数切换播放地址解析请求已发送")
            } else {
                Log.e(TAG, "❌ [集数切换] 当前电影信息为空，无法解析播放地址")
                _uiState.value = _uiState.value.copy(
                    isParsingPlayUrl = false,
                    error = "电影信息丢失，无法切换集数"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ [集数切换] 播放地址解析请求失败", e)
            _uiState.value = _uiState.value.copy(
                isParsingPlayUrl = false,
                error = "集数切换失败: ${e.message}"
            )
        }
    }

    /**
     * 显示线路选择器
     */
    fun showFlagSelector() {
        _uiState.value = _uiState.value.copy(showFlagSelector = true)
    }

    /**
     * 隐藏线路选择器
     */
    fun hideFlagSelector() {
        _uiState.value = _uiState.value.copy(showFlagSelector = false)
    }

    /**
     * 重置自动播放标志
     */
    fun resetAutoPlayFlag() {
        _uiState.value = _uiState.value.copy(shouldAutoPlay = false)
        Log.d(TAG, "🔥 [自动播放] 重置自动播放标志")
    }

    /**
     * 设置内嵌播放器自动播放
     */
    fun setEmbeddedPlayerAutoPlay(autoPlay: Boolean) {
        _uiState.value = _uiState.value.copy(shouldEmbeddedAutoPlay = autoPlay)
        Log.d(TAG, "🔥 [内嵌播放器] 设置自动播放: $autoPlay")
    }

    /**
     * 重置内嵌播放器自动播放标志
     */
    fun resetEmbeddedAutoPlayFlag() {
        _uiState.value = _uiState.value.copy(shouldEmbeddedAutoPlay = false)
        Log.d(TAG, "🔥 [内嵌播放器] 重置自动播放标志")
    }

    /**
     * 显示剧集选择器
     */
    fun showEpisodeSelector() {
        _uiState.value = _uiState.value.copy(showEpisodeSelector = true)
    }

    /**
     * 隐藏剧集选择器
     */
    fun hideEpisodeSelector() {
        _uiState.value = _uiState.value.copy(showEpisodeSelector = false)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 更新播放进度
     * 🔥 修复：正确保存观看历史，包含完整的电影信息
     */
    fun updatePlayProgress(position: Long, duration: Long) {
        // 🔥 修复：通过适配器保存观看历史，包含海报信息
        val currentState = _uiState.value
        val movie = currentState.movie
        val episode = currentState.currentEpisode

        if (movie != null && episode != null && duration > 0) {
            val currentFlowId = if (::currentFlowId.isInitialized) currentFlowId else "UNKNOWN"
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_UPDATE_PROGRESS] 更新播放进度: ${movie.vodName}, 位置=${position}ms, 时长=${duration}ms")

            try {
                // 🔥 海报修复：优先使用传递的海报URL，对标原版FongMi_TV逻辑
                val moviePic = passedVodPic ?: movie.vodPic ?: ""
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_PIC_SOURCE] 海报URL来源: 传递=${passedVodPic}, Spider=${movie.vodPic}, 最终使用=$moviePic")

                // 🔥 修复：调用正确的saveWatchHistory方法，包含海报URL
                repositoryAdapter.saveWatchHistory(
                    movie.vodId,
                    movie.vodName,
                    moviePic, // 🔥 使用优先级海报URL
                    position,
                    duration
                )
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_SAVE_HISTORY_SUCCESS] 观看历史保存成功，包含海报: $moviePic")
            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_SAVE_HISTORY_ERROR] 观看历史保存失败: ${e.message}", e)
            }
        }
    }

    // ===== 私有方法 =====

    /**
     * 处理内容详情成功
     * 🔥 关键修复：添加完整的异常处理，防止死循环
     */
    private fun handleContentDetailSuccess(vod: top.cywin.onetv.movie.bean.Vod) {
        Log.d(TAG, "✅ 处理内容详情成功: ${vod.vodName}")

        try {
            // ✅ 转换为UI模型
            val movieItem = ViewModelAdapter.convertVodToMovie(vod)
            if (movieItem == null) {
                Log.e(TAG, "❌ [详情处理修复] 数据转换失败: ${vod.vodName}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据转换失败"
                )
                return
            }

            // ✅ 解析播放源 - 🔥 关键修复：添加详细日志和异常捕获
            Log.d(TAG, "🎬 [详情处理修复] 开始解析播放线路: ${vod.vodName}")
            Log.d(TAG, "🎬 [详情处理修复] Vod对象信息: vodFlags数量=${vod.vodFlags?.size ?: 0}")

            // 🔥 添加详细的Vod数据日志
            vod.vodFlags?.forEachIndexed { index, flag ->
                Log.d(TAG, "🎬 [详情处理修复] Flag[$index]: name=${flag.flag}, urls长度=${flag.urls?.length ?: 0}")
                if (flag.urls != null && flag.urls.isNotEmpty()) {
                    Log.d(TAG, "🎬 [详情处理修复] Flag[$index] urls前100字符: ${flag.urls.take(100)}")
                }
            }

            val playFlags = try {
                ViewModelAdapter.convertVodFlags(vod)
            } catch (e: Exception) {
                Log.e(TAG, "❌ [详情处理修复] 播放线路转换失败: ${vod.vodName}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "播放线路解析失败: ${e.message}"
                )
                return
            }

            Log.d(TAG, "🎬 [详情处理修复] 播放线路转换完成: 数量=${playFlags.size}")
            if (playFlags.isEmpty()) {
                Log.w(TAG, "⚠️ [详情处理修复] 没有找到播放源: ${vod.vodName}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到播放源"
                )
                return
            }

            // ✅ 选择默认播放源和剧集
            val defaultFlag = playFlags.firstOrNull()
            Log.d(TAG, "🎬 [详情处理修复] 默认播放线路: ${defaultFlag?.flag}, urls长度=${defaultFlag?.urls?.length ?: 0}")

            val episodes = if (defaultFlag != null) {
                Log.d(TAG, "🎬 [详情处理修复] 开始转换剧集，urls内容: ${defaultFlag.urls.take(200)}")
                val convertedEpisodes = ViewModelAdapter.convertVodEpisodes(defaultFlag.urls)
                Log.d(TAG, "🎬 [详情处理修复] 剧集转换完成: 数量=${convertedEpisodes.size}")
                convertedEpisodes.forEachIndexed { index, episode ->
                    Log.d(TAG, "🎬 [详情处理修复] Episode[$index]: name=${episode.name}, url=${episode.url.take(50)}")
                }
                convertedEpisodes
            } else {
                Log.w(TAG, "⚠️ [详情处理修复] 默认播放线路为null")
                emptyList()
            }

            if (episodes.isEmpty()) {
                Log.w(TAG, "⚠️ [详情处理修复] 没有找到可播放的剧集: ${vod.vodName}")
                Log.w(TAG, "⚠️ [详情处理修复] 播放线路数量: ${playFlags.size}")
                Log.w(TAG, "⚠️ [详情处理修复] 默认线路urls: ${defaultFlag?.urls}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到可播放的剧集"
                )
                return
            }

            val targetEpisode = episodes.firstOrNull()
            if (targetEpisode == null) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "剧集信息无效"
                )
                return
            }

            // ✅ 更新UI状态
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                movie = movieItem,
                playFlags = playFlags,
                currentFlag = defaultFlag,
                episodes = episodes,
                currentEpisode = targetEpisode,
                error = null
            )

            // 🔥 新增：自动解析第一个剧集的播放地址
            if (defaultFlag != null) { // 🔥 修复：移除targetEpisode检查，因为前面已经确保不为null
                // 🔥 关键修复：确保FlowID连续性，绝不生成新FlowID！
                val flowIdToUse = if (::currentFlowId.isInitialized) {
                    Log.d(TAG, "🔥 [FlowID连续性] 使用已有FlowID: $currentFlowId")
                    currentFlowId
                } else {
                    // 🔥 尝试从全局FlowID获取
                    val globalFlowId = VodFlowTracker.getGlobalFlowId()
                    if (globalFlowId != null) {
                        Log.d(TAG, "🔥 [FlowID连续性] 从全局FlowID获取: $globalFlowId")
                        currentFlowId = globalFlowId
                        globalFlowId
                    } else {
                        // 🔥 关键修复：如果没有FlowID，使用当前时间戳但保持连续性
                        Log.w(TAG, "⚠️ [FlowID连续性] 全局FlowID为空，使用当前时间戳")
                        val newFlowId = "MOVIE_${System.currentTimeMillis()}"
                        currentFlowId = newFlowId
                        VodFlowTracker.setGlobalFlowId(newFlowId)
                        newFlowId
                    }
                }

                VodFlowTracker.logFlowStep(flowIdToUse, "AUTO_PARSE_START", "开始自动解析第一个剧集: ${targetEpisode.name}")
                Log.d(TAG, "🎬 [FlowID:$flowIdToUse] 开始自动解析第一个剧集: ${targetEpisode.name}")
                Log.d(TAG, "🎬 [FlowID:$flowIdToUse] 剧集URL: ${targetEpisode.url}")
                Log.d(TAG, "🎬 [FlowID:$flowIdToUse] 播放线路: ${defaultFlag.flag}")
                Log.d(TAG, "🎬 [FlowID:$flowIdToUse] 站点Key: ${movieItem.siteKey}")

                // 🔥 设置解析状态
                _uiState.value = _uiState.value.copy(isParsingPlayUrl = true)

                try {
                    // 🔥 关键：设置FlowID上下文，然后调用SiteViewModel.playerContent()解析播放地址
                    VodFlowTracker.logFlowStep(flowIdToUse, "PARSE_PLAY_URL_REQUEST", "发送播放地址解析请求")
                    VodFlowTracker.setCurrentFlowId(flowIdToUse) // 设置FlowID上下文
                    VodFlowTracker.setGlobalFlowId(flowIdToUse) // 🔥 同时设置全局FlowID，确保跨页面传递
                    Log.d(TAG, "🔥 [FlowID修复] 设置上下文FlowID: $flowIdToUse")
                    Log.d(TAG, "🔥 [FlowID修复] 设置全局FlowID: $flowIdToUse")
                    repositoryAdapter.parsePlayUrl(targetEpisode.url, movieItem.siteKey, defaultFlag.flag)
                    Log.d(TAG, "✅ [FlowID:$flowIdToUse] 播放地址解析请求已发送")

                } catch (e: Exception) {
                    VodFlowTracker.logFlowError(flowIdToUse, "PARSE_PLAY_URL", "播放地址解析请求失败", e)
                    Log.e(TAG, "❌ [FlowID:$flowIdToUse] 播放地址解析请求失败", e)
                    _uiState.value = _uiState.value.copy(
                        isParsingPlayUrl = false,
                        error = "播放地址解析失败: ${e.message}"
                    )
                }
            } else {
                Log.w(TAG, "⚠️ 无法解析播放地址: targetEpisode=${targetEpisode?.name}, defaultFlag=${defaultFlag?.flag}")
            }



        } catch (e: Exception) {
            Log.e(TAG, "💥 详情处理失败", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "详情处理失败: ${e.message}"
            )
        }
    }

    /**
     * 检查收藏状态和观看历史
     * 🔥 海报修复：添加海报URL参数，对标原版FongMi_TV
     */
    private suspend fun checkFavoriteAndHistory(vodId: String, siteKey: String, vodPic: String? = null) {
        try {
            // 🔥 修复：添加对标原版FongMi_TV的观看历史检查日志
            val currentMovie = _uiState.value.movie
            val movieName = currentMovie?.vodName ?: "未知影片"
            // 🔥 海报修复：优先使用传入的海报URL，对标原版FongMi_TV的checkHistory逻辑
            val moviePic = vodPic ?: currentMovie?.vodPic ?: ""

            if (::currentFlowId.isInitialized) {
                Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_CHECK_START] 开始检查观看历史 电影: $movieName, ID: $vodId, 海报: $moviePic")
                if (!vodPic.isNullOrEmpty()) {
                    Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [DETAIL_PIC_USE_PASSED] 使用搜索结果传递的海报URL: $vodPic")
                }
            }

            // ✅ 检查收藏状态
            val isFavorite = repositoryAdapter.isFavorite(vodId, siteKey)

            // ✅ 获取观看历史
            val history = repositoryAdapter.getWatchHistory(vodId, siteKey)
            val watchHistory = if (history != null) {
                // 🔥 对标原版FongMi_TV的HISTORY_FOUND日志
                if (::currentFlowId.isInitialized) {
                    Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_FOUND] 找到已存在的观看历史记录 Key: ${history.key}, 上次观看位置: ${history.position}, 时长: ${history.duration}")

                    // 🔥 对标原版FongMi_TV的HISTORY_PIC_UPDATE日志
                    if (!moviePic.isEmpty()) {
                        Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_PIC_UPDATE] 更新观看历史海报 旧海报: ${history.vodPic ?: "无"}, 新海报: $moviePic")
                    }

                    Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_CHECK_COMPLETE] 观看历史检查完成 电影: $movieName, 站点: ${VodConfig.getCid()}, 开头跳过: ${history.opening}, 结尾跳过: ${history.ending}")
                }

                WatchHistory(
                    vodId = history.vodId,
                    vodName = history.vodName,
                    vodPic = history.vodPic ?: moviePic, // ✅ 优先使用历史记录中的海报，如果没有则使用当前电影海报
                    siteKey = siteKey,
                    episodeName = history.vodRemarks ?: "",
                    position = history.position,
                    duration = history.duration,
                    watchTime = history.createTime,
                    isCompleted = history.position >= history.duration * 0.9
                )
            } else {
                // 🔥 对标原版FongMi_TV的日志：没有找到观看历史
                if (::currentFlowId.isInitialized) {
                    Log.i("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_NOT_FOUND] 未找到观看历史记录 电影: $movieName, ID: $vodId")
                }
                null
            }

            // ✅ 更新UI状态
            _uiState.value = _uiState.value.copy(
                isFavorite = isFavorite,
                watchHistory = watchHistory
            )

        } catch (e: Exception) {
            Log.e(TAG, "💥 检查收藏和历史失败", e)
            if (::currentFlowId.isInitialized) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_CHECK_ERROR] 观看历史检查失败: ${e.message}")
            }
        }
    }

    /**
     * 转换为FongMi_TV的Vod对象
     * 🔥 海报修复：优先使用传递的海报URL
     */
    private fun convertToFongMiVod(movie: MovieItem): top.cywin.onetv.movie.bean.Vod {
        val vod = top.cywin.onetv.movie.bean.Vod()
        vod.setVodId(movie.vodId)
        vod.setVodName(movie.vodName)

        // 🔥 海报修复：优先使用传递的海报URL，对标原版FongMi_TV逻辑
        val moviePic = passedVodPic ?: movie.vodPic ?: ""
        vod.setVodPic(moviePic)

        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITE_PIC_SOURCE] 收藏海报URL来源: 传递=${passedVodPic}, Spider=${movie.vodPic}, 最终使用=$moviePic")

        // 🔥 使用反射设置私有字段，因为Vod类缺少这些字段的setter方法
        try {
            // 设置vodRemarks字段
            if (!movie.vodRemarks.isNullOrEmpty()) {
                val vodRemarksField = vod.javaClass.getDeclaredField("vodRemarks")
                vodRemarksField.isAccessible = true
                vodRemarksField.set(vod, movie.vodRemarks)
            }

            // 设置vodYear字段
            if (!movie.vodYear.isNullOrEmpty()) {
                val vodYearField = vod.javaClass.getDeclaredField("vodYear")
                vodYearField.isAccessible = true
                vodYearField.set(vod, movie.vodYear)
            }

            // 设置vodArea字段
            if (!movie.vodArea.isNullOrEmpty()) {
                val vodAreaField = vod.javaClass.getDeclaredField("vodArea")
                vodAreaField.isAccessible = true
                vodAreaField.set(vod, movie.vodArea)
            }

            // 设置vodDirector字段
            if (!movie.vodDirector.isNullOrEmpty()) {
                val vodDirectorField = vod.javaClass.getDeclaredField("vodDirector")
                vodDirectorField.isAccessible = true
                vodDirectorField.set(vod, movie.vodDirector)
            }

            // 设置vodActor字段
            if (!movie.vodActor.isNullOrEmpty()) {
                val vodActorField = vod.javaClass.getDeclaredField("vodActor")
                vodActorField.isAccessible = true
                vodActorField.set(vod, movie.vodActor)
            }

            // 设置vodContent字段
            if (!movie.vodContent.isNullOrEmpty()) {
                val vodContentField = vod.javaClass.getDeclaredField("vodContent")
                vodContentField.isAccessible = true
                vodContentField.set(vod, movie.vodContent)
            }

            // 注意：MovieItem没有typeName字段，跳过此字段设置

            android.util.Log.d(TAG, "[VOD_CONVERT] Vod对象字段设置完成: ${movie.vodName}")

        } catch (e: Exception) {
            android.util.Log.e(TAG, "[VOD_CONVERT] 反射设置Vod字段失败", e)
        }

        // 设置站点信息
        val site = repositoryAdapter.getCurrentSite()
        vod.setSite(site)

        return vod
    }

    /**
     * 检查收藏状态 - 辅助方法
     */
    private suspend fun checkFavoriteStatus(vodId: String, siteKey: String): Boolean {
        return try {
            repositoryAdapter.isFavorite(vodId, siteKey)
        } catch (e: Exception) {
            Log.e(TAG, "💥 检查收藏状态失败", e)
            false
        }
    }

    /**
     * 获取观看历史 - 辅助方法
     */
    private suspend fun getWatchHistory(vodId: String, siteKey: String): WatchHistory? {
        return try {
            val history = repositoryAdapter.getWatchHistory(vodId, siteKey)
            if (history != null) {
                WatchHistory(
                    vodId = history.vodId,
                    vodName = history.vodName,
                    vodPic = history.vodPic ?: "", // ✅ 添加海报字段
                    siteKey = siteKey,
                    episodeName = history.vodRemarks ?: "",
                    position = history.position,
                    duration = history.duration,
                    watchTime = history.createTime,
                    isCompleted = history.position >= history.duration * 0.9
                )
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "💥 获取观看历史失败", e)
            null
        }
    }


}
